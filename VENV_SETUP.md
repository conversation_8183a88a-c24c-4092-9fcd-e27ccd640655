# Python 虚拟环境设置指南

## 概述

本项目已配置了Python虚拟环境，用于隔离项目依赖，避免与系统Python环境冲突。

## 虚拟环境信息

- **Python版本**: 3.9.6
- **虚拟环境目录**: `venv/`
- **依赖文件**: `requirements.txt`

## 使用方法

### 方法1：使用管理脚本（推荐）

```bash
# 查看所有可用命令
./manage.sh help

# 启动Web服务
./manage.sh web

# 运行数据处理任务
./manage.sh job

# 查看服务状态
./manage.sh status

# 停止所有服务
./manage.sh stop
```

### 方法2：使用单独的启动脚本

```bash
# 启动Web服务
./start_web.sh

# 运行数据任务
./start_job.sh

# 启动交易服务
./start_trade.sh
```

### 方法3：手动激活虚拟环境

```bash
# 激活虚拟环境
source venv/bin/activate

# 验证环境
python --version
pip --version

# 退出虚拟环境
deactivate
```

## 依赖管理

### 安装依赖

```bash
# 确保虚拟环境已激活
source venv/bin/activate

# 安装所有依赖
pip install -r requirements.txt
```

### 添加新依赖

```bash
# 安装新包
pip install package_name

# 更新requirements.txt
pip freeze > requirements.txt
```

## 当前已安装的主要依赖

- **numpy**: 1.26.4 - 数值计算
- **pandas**: 2.0.3 - 数据处理
- **requests**: 2.32.3 - HTTP请求
- **beautifulsoup4**: 4.13.4 - HTML解析
- **bokeh**: 3.4.3 - 数据可视化
- **SQLAlchemy**: 2.0.41 - 数据库ORM
- **tornado**: 6.5.1 - Web框架
- **PyMySQL**: 1.1.1 - MySQL连接器

## 注意事项

### 暂未安装的依赖

以下依赖由于系统兼容性问题暂未安装：

1. **TA-Lib**: 需要先安装系统依赖
   ```bash
   # macOS (需要Homebrew)
   brew install ta-lib
   pip install TA-Lib==0.4.28
   
   # Ubuntu/Debian
   sudo apt-get install libta-lib-dev
   pip install TA-Lib==0.4.28
   ```

2. **easytrader**: 依赖pywinauto，在macOS上可能有兼容性问题

### 环境重建

如果需要重新创建虚拟环境：

```bash
# 删除现有环境
rm -rf venv/

# 创建新环境
python3 -m venv venv

# 激活环境
source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt
```

## 故障排除

### 常见问题

1. **权限错误**: 确保有写入权限
2. **Python版本不匹配**: 检查系统Python版本
3. **依赖冲突**: 使用虚拟环境隔离依赖

### 获取帮助

如果遇到问题，请检查：
1. Python版本是否正确
2. 虚拟环境是否已激活
3. 依赖是否正确安装

## 启动脚本说明

### 管理脚本
- `manage.sh`: 主管理脚本，提供统一的服务管理接口
- `activate_venv.sh`: 激活虚拟环境的便捷脚本

### 服务启动脚本
- `start_web.sh`: 启动Web服务（端口9988）
- `start_job.sh`: 运行数据处理任务
- `start_trade.sh`: 启动交易服务

### 已修改的原始脚本
以下脚本已修改为使用venv环境：
- `instock/bin/run_web.sh`: Web服务启动脚本
- `instock/bin/run_job.sh`: 数据任务启动脚本
- `instock/bin/restart_web.sh`: Web服务重启脚本
- `cron/cron.hourly/run_hourly`: 小时级定时任务
- `cron/cron.workdayly/run_workdayly`: 工作日定时任务

## 项目结构

```
stock/
├── venv/                    # 虚拟环境目录
├── requirements.txt         # 依赖列表
├── manage.sh               # 主管理脚本 ⭐
├── activate_venv.sh        # 激活脚本
├── start_web.sh            # Web服务启动脚本 ⭐
├── start_job.sh            # 数据任务启动脚本 ⭐
├── start_trade.sh          # 交易服务启动脚本 ⭐
├── VENV_SETUP.md           # 本文档
├── instock/                # 主要代码目录
│   ├── bin/                # 启动脚本（已修改）
│   ├── web/                # Web服务
│   ├── job/                # 数据处理任务
│   └── trade/              # 交易服务
├── cron/                   # 定时任务（已修改）
└── ...                     # 其他项目文件
```
