# Python 虚拟环境设置指南

## 概述

本项目已配置了Python虚拟环境，用于隔离项目依赖，避免与系统Python环境冲突。

## 虚拟环境信息

- **Python版本**: 3.9.6
- **虚拟环境目录**: `venv/`
- **依赖文件**: `requirements.txt`

## 使用方法

### 方法1：使用便捷脚本（推荐）

```bash
# 激活虚拟环境
chmod +x activate_venv.sh
./activate_venv.sh
```

### 方法2：手动激活

```bash
# 激活虚拟环境
source venv/bin/activate

# 验证环境
python --version
pip --version

# 退出虚拟环境
deactivate
```

## 依赖管理

### 安装依赖

```bash
# 确保虚拟环境已激活
source venv/bin/activate

# 安装所有依赖
pip install -r requirements.txt
```

### 添加新依赖

```bash
# 安装新包
pip install package_name

# 更新requirements.txt
pip freeze > requirements.txt
```

## 当前已安装的主要依赖

- **numpy**: 1.26.4 - 数值计算
- **pandas**: 2.0.3 - 数据处理
- **requests**: 2.32.3 - HTTP请求
- **beautifulsoup4**: 4.13.4 - HTML解析
- **bokeh**: 3.4.3 - 数据可视化
- **SQLAlchemy**: 2.0.41 - 数据库ORM
- **tornado**: 6.5.1 - Web框架
- **PyMySQL**: 1.1.1 - MySQL连接器

## 注意事项

### 暂未安装的依赖

以下依赖由于系统兼容性问题暂未安装：

1. **TA-Lib**: 需要先安装系统依赖
   ```bash
   # macOS (需要Homebrew)
   brew install ta-lib
   pip install TA-Lib==0.4.28
   
   # Ubuntu/Debian
   sudo apt-get install libta-lib-dev
   pip install TA-Lib==0.4.28
   ```

2. **easytrader**: 依赖pywinauto，在macOS上可能有兼容性问题

### 环境重建

如果需要重新创建虚拟环境：

```bash
# 删除现有环境
rm -rf venv/

# 创建新环境
python3 -m venv venv

# 激活环境
source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt
```

## 故障排除

### 常见问题

1. **权限错误**: 确保有写入权限
2. **Python版本不匹配**: 检查系统Python版本
3. **依赖冲突**: 使用虚拟环境隔离依赖

### 获取帮助

如果遇到问题，请检查：
1. Python版本是否正确
2. 虚拟环境是否已激活
3. 依赖是否正确安装

## 项目结构

```
stock/
├── venv/                 # 虚拟环境目录
├── requirements.txt      # 依赖列表
├── activate_venv.sh     # 激活脚本
├── VENV_SETUP.md        # 本文档
└── ...                  # 其他项目文件
```
