#!/bin/bash
# 数据库诊断和修复脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 数据库诊断和修复工具${NC}"
echo ""

# 检查是否在项目根目录
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}❌ 错误：请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo -e "${RED}❌ 错误：虚拟环境不存在${NC}"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

echo -e "${BLUE}📊 检查数据库状态...${NC}"

python << 'EOF'
import sys
sys.path.append('.')
import instock.lib.database as db
import pymysql

def check_database_and_tables():
    try:
        print("🔄 连接数据库...")
        conn = db.get_connection()
        if not conn:
            print("❌ 无法连接到数据库")
            return False
            
        with conn.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db.db_database}'")
            db_exists = cursor.fetchone()
            
            if not db_exists:
                print(f"❌ 数据库 '{db.db_database}' 不存在")
                return False
            else:
                print(f"✅ 数据库 '{db.db_database}' 存在")
            
            # 检查关键表是否存在
            required_tables = [
                'cn_stock_attention',
                'cn_stock_selection', 
                'cn_stock_spot'
            ]
            
            missing_tables = []
            existing_tables = []
            
            for table in required_tables:
                cursor.execute(f"SHOW TABLES LIKE '{table}'")
                table_exists = cursor.fetchone()
                if table_exists:
                    existing_tables.append(table)
                    print(f"✅ 表 '{table}' 存在")
                else:
                    missing_tables.append(table)
                    print(f"❌ 表 '{table}' 不存在")
            
            # 检查现有表的数据
            if existing_tables:
                print("\n📊 检查表数据...")
                for table in existing_tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"📋 表 '{table}': {count} 条记录")
            
            conn.close()
            
            if missing_tables:
                print(f"\n⚠️  缺失的表: {', '.join(missing_tables)}")
                print("💡 建议运行修复命令")
                return False
            else:
                print("\n🎉 所有关键表都存在！")
                return True
                
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        return False

def create_missing_tables():
    try:
        print("\n🔧 创建缺失的表...")
        
        # 连接到mysql数据库（不是instockdb）
        conn_config = db.MYSQL_CONN_DBAPI.copy()
        conn_config['database'] = 'mysql'
        
        with pymysql.connect(**conn_config) as conn:
            with conn.cursor() as cursor:
                # 创建数据库（如果不存在）
                create_db_sql = f"CREATE DATABASE IF NOT EXISTS `{db.db_database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci"
                cursor.execute(create_db_sql)
                print(f"✅ 确保数据库 '{db.db_database}' 存在")
        
        # 连接到目标数据库
        with pymysql.connect(**db.MYSQL_CONN_DBAPI) as conn:
            with conn.cursor() as cursor:
                # 创建 cn_stock_attention 表
                create_attention_sql = """
                CREATE TABLE IF NOT EXISTS `cn_stock_attention` (
                    `datetime` datetime(0) NULL DEFAULT NULL, 
                    `code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                    PRIMARY KEY (`code`) USING BTREE,
                    INDEX `INIX_DATETIME`(`datetime`) USING BTREE
                ) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
                """
                cursor.execute(create_attention_sql)
                print("✅ 创建表 'cn_stock_attention'")
                
                # 检查其他表是否需要创建
                cursor.execute("SHOW TABLES")
                existing_tables = [table[0] for table in cursor.fetchall()]
                
                print(f"📋 当前数据库中的表: {', '.join(existing_tables)}")
                
        print("🎉 表创建完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建表时出错: {e}")
        return False

# 主逻辑
if __name__ == "__main__":
    success = check_database_and_tables()
    
    if not success:
        print(f"\n{'-'*50}")
        response = input("🤔 是否要尝试修复缺失的表？(y/n): ")
        if response.lower() in ['y', 'yes']:
            if create_missing_tables():
                print("\n🔄 重新检查数据库状态...")
                check_database_and_tables()
            else:
                print("\n❌ 修复失败，请检查数据库权限和配置")
        else:
            print("\n💡 建议手动运行: ./manage.sh job init")
    else:
        print("\n🎉 数据库状态正常！")
EOF

echo ""
echo -e "${BLUE}📚 更多帮助：${NC}"
echo "- 重新初始化数据库: ./manage.sh job init"
echo "- 测试数据库连接: ./manage.sh test-db"
echo "- 查看配置指南: cat DATABASE_SETUP.md"
