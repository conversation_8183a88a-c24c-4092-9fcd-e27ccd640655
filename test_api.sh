#!/bin/bash
# API测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌐 API测试工具${NC}"
echo ""

# 检查Web服务是否运行
WEB_PID=$(ps aux | grep "instock/web/web_service.py" | grep -v grep | awk '{print $2}')
if [ -z "$WEB_PID" ]; then
    echo -e "${RED}❌ Web服务未运行${NC}"
    echo "请先启动Web服务: ./manage.sh web"
    exit 1
else
    echo -e "${GREEN}✅ Web服务运行中 (PID: $WEB_PID)${NC}"
fi

echo ""
echo -e "${BLUE}🔍 测试API接口...${NC}"

# 测试主页
echo "1. 测试主页..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:9988/)
if [ "$response" = "200" ]; then
    echo -e "   ${GREEN}✅ 主页访问正常 (HTTP $response)${NC}"
else
    echo -e "   ${RED}❌ 主页访问失败 (HTTP $response)${NC}"
fi

# 测试数据页面
echo "2. 测试数据页面..."
response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:9988/instock/data?table_name=cn_stock_selection")
if [ "$response" = "200" ]; then
    echo -e "   ${GREEN}✅ 数据页面访问正常 (HTTP $response)${NC}"
else
    echo -e "   ${RED}❌ 数据页面访问失败 (HTTP $response)${NC}"
fi

# 测试API数据接口
echo "3. 测试API数据接口..."
api_url="http://localhost:9988/instock/api_data?name=cn_stock_selection&date=2025-06-13"
echo "   URL: $api_url"

response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$api_url")
http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')

if [ "$http_code" = "200" ]; then
    echo -e "   ${GREEN}✅ API接口访问正常 (HTTP $http_code)${NC}"
    
    # 检查返回的数据
    if echo "$body" | grep -q "data"; then
        echo -e "   ${GREEN}✅ 返回数据格式正确${NC}"
        
        # 统计数据条数
        data_count=$(echo "$body" | grep -o '"data":\[' | wc -l)
        if [ "$data_count" -gt 0 ]; then
            echo -e "   ${GREEN}✅ 包含数据内容${NC}"
        else
            echo -e "   ${YELLOW}⚠️  数据为空${NC}"
        fi
    else
        echo -e "   ${YELLOW}⚠️  返回数据格式异常${NC}"
        echo "   响应内容: ${body:0:200}..."
    fi
else
    echo -e "   ${RED}❌ API接口访问失败 (HTTP $http_code)${NC}"
    echo "   错误信息: ${body:0:200}..."
fi

# 测试其他API接口
echo "4. 测试其他数据接口..."
other_apis=(
    "cn_stock_spot"
    "cn_stock_indicators"
)

for api in "${other_apis[@]}"; do
    echo "   测试 $api..."
    response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:9988/instock/api_data?name=$api")
    if [ "$response" = "200" ]; then
        echo -e "     ${GREEN}✅ $api 正常 (HTTP $response)${NC}"
    else
        echo -e "     ${YELLOW}⚠️  $api 异常 (HTTP $response)${NC}"
    fi
done

echo ""
echo -e "${BLUE}📊 测试总结${NC}"
echo "- Web服务地址: http://localhost:9988/"
echo "- 主要数据接口: http://localhost:9988/instock/data?table_name=cn_stock_selection"
echo "- API数据接口: http://localhost:9988/instock/api_data?name=cn_stock_selection"

echo ""
echo -e "${GREEN}🎉 测试完成！${NC}"
echo "如果所有测试都通过，您可以在浏览器中访问 http://localhost:9988/ 查看股票分析界面"
