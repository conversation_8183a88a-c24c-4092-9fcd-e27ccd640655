#!/bin/bash
# 启动数据处理任务的便捷脚本（使用venv环境）

# 检查是否在项目根目录
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "❌ 错误：虚拟环境不存在，请先运行 python3 -m venv venv"
    exit 1
fi

echo "📊 启动股票数据处理任务..."

# 激活虚拟环境
source venv/bin/activate

# 检查依赖是否安装
python -c "import pandas, numpy, requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  警告：依赖包未完全安装，正在安装..."
    pip install -r requirements.txt
fi

echo "✅ 虚拟环境已激活"

# 显示可用的任务选项
echo ""
echo "📋 可用的任务选项："
echo "1. 完整数据处理任务 (execute_daily_job.py)"
echo "2. 基础数据任务 (basic_data_daily_job.py)"
echo "3. 选股数据任务 (selection_data_daily_job.py)"
echo "4. 指标数据任务 (indicators_data_daily_job.py)"
echo "5. 策略数据任务 (strategy_data_daily_job.py)"
echo ""

# 检查命令行参数
if [ $# -eq 0 ]; then
    echo "🔄 运行完整数据处理任务..."
    python instock/job/execute_daily_job.py
elif [ "$1" = "basic" ]; then
    echo "🔄 运行基础数据任务..."
    python instock/job/basic_data_daily_job.py
elif [ "$1" = "selection" ]; then
    echo "🔄 运行选股数据任务..."
    python instock/job/selection_data_daily_job.py
elif [ "$1" = "indicators" ]; then
    echo "🔄 运行指标数据任务..."
    python instock/job/indicators_data_daily_job.py
elif [ "$1" = "strategy" ]; then
    echo "🔄 运行策略数据任务..."
    python instock/job/strategy_data_daily_job.py
elif [ "$1" = "init" ]; then
    echo "🔄 运行数据库初始化任务..."
    python instock/job/init_job.py
else
    echo "🔄 运行完整数据处理任务（带参数：$@）..."
    python instock/job/execute_daily_job.py "$@"
fi

echo "✅ 任务执行完成"
