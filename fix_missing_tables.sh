#!/bin/bash
# 修复缺失数据库表的脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 修复缺失的数据库表${NC}"
echo ""

# 检查是否在项目根目录
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}❌ 错误：请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo -e "${RED}❌ 错误：虚拟环境不存在${NC}"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

echo -e "${BLUE}📊 检查并创建缺失的表...${NC}"

python << 'EOF'
import sys
sys.path.append('.')
import instock.lib.database as db
import instock.core.tablestructure as tbs
import pymysql

def create_missing_tables():
    try:
        print("🔄 连接数据库...")
        
        # 检查数据库连接
        conn = db.get_connection()
        if not conn:
            print("❌ 无法连接到数据库")
            return False
        
        with conn.cursor() as cursor:
            # 检查当前存在的表
            cursor.execute("SHOW TABLES")
            existing_tables = [table[0] for table in cursor.fetchall()]
            print(f"📋 当前存在的表: {', '.join(existing_tables)}")
            
            # 需要创建的表列表
            required_tables = {
                'cn_stock_attention': {
                    'sql': """
                    CREATE TABLE IF NOT EXISTS `cn_stock_attention` (
                        `datetime` datetime(0) NULL DEFAULT NULL, 
                        `code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                        PRIMARY KEY (`code`) USING BTREE,
                        INDEX `INIX_DATETIME`(`datetime`) USING BTREE
                    ) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
                    """,
                    'description': '我的关注表'
                },
                'cn_stock_pattern': {
                    'sql': """
                    CREATE TABLE IF NOT EXISTS `cn_stock_pattern` (
                        `date` date NOT NULL,
                        `code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                        `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                        `tow_crows` smallint NULL DEFAULT NULL,
                        `upside_gap_two_crows` smallint NULL DEFAULT NULL,
                        `three_black_crows` smallint NULL DEFAULT NULL,
                        `identical_three_crows` smallint NULL DEFAULT NULL,
                        `three_line_strike` smallint NULL DEFAULT NULL,
                        `dark_cloud_cover` smallint NULL DEFAULT NULL,
                        `evening_doji_star` smallint NULL DEFAULT NULL,
                        `evening_star` smallint NULL DEFAULT NULL,
                        `hanging_man` smallint NULL DEFAULT NULL,
                        `hammer` smallint NULL DEFAULT NULL,
                        `inverted_hammer` smallint NULL DEFAULT NULL,
                        `shooting_star` smallint NULL DEFAULT NULL,
                        `doji` smallint NULL DEFAULT NULL,
                        `doji_star` smallint NULL DEFAULT NULL,
                        `dragonfly_doji` smallint NULL DEFAULT NULL,
                        `gravestone_doji` smallint NULL DEFAULT NULL,
                        `long_legged_doji` smallint NULL DEFAULT NULL,
                        `marubozu` smallint NULL DEFAULT NULL,
                        `spinning_top` smallint NULL DEFAULT NULL,
                        PRIMARY KEY (`date`, `code`) USING BTREE,
                        INDEX `IX_CODE`(`code`) USING BTREE,
                        INDEX `IX_DATE`(`date`) USING BTREE
                    ) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
                    """,
                    'description': 'K线形态表'
                },
                'cn_stock_indicators': {
                    'sql': """
                    CREATE TABLE IF NOT EXISTS `cn_stock_indicators` (
                        `date` date NOT NULL,
                        `code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                        `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                        `macd` float NULL DEFAULT NULL,
                        `macd_signal` float NULL DEFAULT NULL,
                        `macd_hist` float NULL DEFAULT NULL,
                        `rsi` float NULL DEFAULT NULL,
                        `kdj_k` float NULL DEFAULT NULL,
                        `kdj_d` float NULL DEFAULT NULL,
                        `kdj_j` float NULL DEFAULT NULL,
                        `boll_upper` float NULL DEFAULT NULL,
                        `boll_mid` float NULL DEFAULT NULL,
                        `boll_lower` float NULL DEFAULT NULL,
                        `ma5` float NULL DEFAULT NULL,
                        `ma10` float NULL DEFAULT NULL,
                        `ma20` float NULL DEFAULT NULL,
                        `ma60` float NULL DEFAULT NULL,
                        `ma120` float NULL DEFAULT NULL,
                        `ma250` float NULL DEFAULT NULL,
                        PRIMARY KEY (`date`, `code`) USING BTREE,
                        INDEX `IX_CODE`(`code`) USING BTREE,
                        INDEX `IX_DATE`(`date`) USING BTREE
                    ) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
                    """,
                    'description': '技术指标表'
                }
            }
            
            # 创建缺失的表
            created_count = 0
            for table_name, table_info in required_tables.items():
                if table_name not in existing_tables:
                    print(f"🔧 创建表: {table_name} ({table_info['description']})")
                    try:
                        cursor.execute(table_info['sql'])
                        print(f"✅ 表 '{table_name}' 创建成功")
                        created_count += 1
                    except Exception as e:
                        print(f"❌ 创建表 '{table_name}' 失败: {e}")
                else:
                    print(f"✅ 表 '{table_name}' 已存在")
            
            # 再次检查表
            cursor.execute("SHOW TABLES")
            final_tables = [table[0] for table in cursor.fetchall()]
            print(f"\n📋 最终表列表: {', '.join(sorted(final_tables))}")
            print(f"📊 总共 {len(final_tables)} 个表")
            
            if created_count > 0:
                print(f"🎉 成功创建了 {created_count} 个表！")
            else:
                print("ℹ️  没有需要创建的表")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False

# 执行创建表操作
if __name__ == "__main__":
    success = create_missing_tables()
    if success:
        print("\n🎉 数据库表修复完成！")
        print("💡 现在可以测试API接口了")
    else:
        print("\n❌ 修复失败，请检查数据库配置")
EOF

echo ""
echo -e "${BLUE}📚 下一步：${NC}"
echo "- 测试API: ./test_api.sh"
echo "- 运行数据任务: ./manage.sh job"
echo "- 查看服务状态: ./manage.sh status"
