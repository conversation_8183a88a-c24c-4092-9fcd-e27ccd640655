#!/bin/bash
# 数据库连接测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 数据库连接测试${NC}"
echo ""

# 检查是否在项目根目录
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}❌ 错误：请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo -e "${RED}❌ 错误：虚拟环境不存在${NC}"
    exit 1
fi

echo -e "${BLUE}📊 当前数据库配置：${NC}"

# 激活虚拟环境并显示配置
source venv/bin/activate

python << 'EOF'
import os
import sys
sys.path.append('.')
import instock.lib.database as db

print(f"主机: {db.db_host}")
print(f"用户: {db.db_user}")
print(f"密码: {'*' * len(db.db_password)}")
print(f"数据库: {db.db_database}")
print(f"端口: {db.db_port}")
print(f"字符集: {db.db_charset}")
print("")

# 测试连接
print("🔄 测试数据库连接...")
try:
    conn = db.get_connection()
    if conn:
        print("✅ 数据库连接成功！")
        
        # 测试基本查询
        with conn.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"📊 MySQL 版本: {version[0]}")
            
            # 检查数据库是否存在
            cursor.execute(f"SHOW DATABASES LIKE '{db.db_database}'")
            db_exists = cursor.fetchone()
            if db_exists:
                print(f"✅ 数据库 '{db.db_database}' 已存在")
            else:
                print(f"⚠️  数据库 '{db.db_database}' 不存在，需要初始化")
        
        conn.close()
        print("")
        print("🎉 数据库配置正确！可以运行项目了。")
        print("💡 下一步：运行 './manage.sh job init' 初始化数据库")
        
    else:
        print("❌ 数据库连接失败：无法获取连接对象")
        
except Exception as e:
    print(f"❌ 数据库连接失败：{e}")
    print("")
    print("🛠️  可能的解决方案：")
    print("1. 检查 MySQL 服务是否运行：brew services list | grep mysql")
    print("2. 启动 MySQL 服务：brew services start mysql")
    print("3. 检查用户名和密码是否正确")
    print("4. 确认用户具有数据库访问权限")
    print("5. 查看详细配置指南：cat DATABASE_SETUP.md")
EOF

echo ""
echo -e "${BLUE}📚 更多帮助：${NC}"
echo "- 数据库配置指南: cat DATABASE_SETUP.md"
echo "- 快速启动指南: cat QUICK_START.md"
echo "- 环境配置说明: cat VENV_SETUP.md"
