#!/bin/bash
# 股票分析系统管理脚本（使用venv环境）

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否在项目根目录
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}❌ 错误：请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo -e "${RED}❌ 错误：虚拟环境不存在，请先运行 python3 -m venv venv${NC}"
    exit 1
fi

# 显示帮助信息
show_help() {
    echo -e "${BLUE}📈 股票分析系统管理工具${NC}"
    echo ""
    echo -e "${GREEN}使用方法:${NC}"
    echo "  ./manage.sh [命令] [参数]"
    echo ""
    echo -e "${GREEN}可用命令:${NC}"
    echo "  web              启动Web服务 (端口9988)"
    echo "  job              运行数据处理任务"
    echo "  job basic        运行基础数据任务"
    echo "  job selection    运行选股数据任务"
    echo "  job indicators   运行指标数据任务"
    echo "  job strategy     运行策略数据任务"
    echo "  job init         运行数据库初始化"
    echo "  trade            启动交易服务"
    echo "  status           查看服务状态"
    echo "  stop             停止所有服务"
    echo "  restart          重启Web服务"
    echo "  env              激活虚拟环境"
    echo "  install          安装/更新依赖"
    echo "  test-db          测试数据库连接"
    echo "  help             显示此帮助信息"
    echo ""
    echo -e "${GREEN}示例:${NC}"
    echo "  ./manage.sh test-db                # 测试数据库连接"
    echo "  ./manage.sh web                    # 启动Web服务"
    echo "  ./manage.sh job                    # 运行完整数据任务"
    echo "  ./manage.sh job basic              # 运行基础数据任务"
    echo "  ./manage.sh status                 # 查看服务状态"
}

# 检查服务状态
check_status() {
    echo -e "${BLUE}📊 服务状态检查${NC}"
    echo ""
    
    # 检查Web服务
    WEB_PID=$(ps aux | grep "instock/web/web_service.py" | grep -v grep | awk '{print $2}')
    if [ -n "$WEB_PID" ]; then
        echo -e "${GREEN}✅ Web服务运行中 (PID: $WEB_PID)${NC}"
        echo "   访问地址: http://localhost:9988/"
    else
        echo -e "${YELLOW}⚠️  Web服务未运行${NC}"
    fi
    
    # 检查交易服务
    TRADE_PID=$(ps aux | grep "instock/trade/trade_service.py" | grep -v grep | awk '{print $2}')
    if [ -n "$TRADE_PID" ]; then
        echo -e "${GREEN}✅ 交易服务运行中 (PID: $TRADE_PID)${NC}"
    else
        echo -e "${YELLOW}⚠️  交易服务未运行${NC}"
    fi
    
    # 检查虚拟环境
    if [ -d "venv" ]; then
        echo -e "${GREEN}✅ 虚拟环境已创建${NC}"
    else
        echo -e "${RED}❌ 虚拟环境不存在${NC}"
    fi
}

# 停止所有服务
stop_services() {
    echo -e "${YELLOW}🛑 停止所有服务...${NC}"
    
    # 停止Web服务
    WEB_PID=$(ps aux | grep "instock/web/web_service.py" | grep -v grep | awk '{print $2}')
    if [ -n "$WEB_PID" ]; then
        kill -9 $WEB_PID
        echo -e "${GREEN}✅ Web服务已停止${NC}"
    fi
    
    # 停止交易服务
    TRADE_PID=$(ps aux | grep "instock/trade/trade_service.py" | grep -v grep | awk '{print $2}')
    if [ -n "$TRADE_PID" ]; then
        kill -9 $TRADE_PID
        echo -e "${GREEN}✅ 交易服务已停止${NC}"
    fi
}

# 安装依赖
install_deps() {
    echo -e "${BLUE}📦 安装/更新依赖包...${NC}"
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 主逻辑
case "$1" in
    "web")
        echo -e "${BLUE}🌐 启动Web服务...${NC}"
        ./start_web.sh
        ;;
    "job")
        if [ -n "$2" ]; then
            echo -e "${BLUE}📊 运行数据任务: $2${NC}"
            ./start_job.sh "$2"
        else
            echo -e "${BLUE}📊 运行完整数据任务...${NC}"
            ./start_job.sh
        fi
        ;;
    "trade")
        echo -e "${BLUE}💰 启动交易服务...${NC}"
        ./start_trade.sh
        ;;
    "status")
        check_status
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        echo -e "${BLUE}🔄 重启Web服务...${NC}"
        ./instock/bin/restart_web.sh
        echo -e "${GREEN}✅ Web服务已重启${NC}"
        ;;
    "env")
        echo -e "${BLUE}🐍 激活虚拟环境...${NC}"
        ./activate_venv.sh
        ;;
    "install")
        install_deps
        ;;
    "test-db")
        echo -e "${BLUE}🔍 测试数据库连接...${NC}"
        ./test_database.sh
        ;;
    "help"|"")
        show_help
        ;;
    *)
        echo -e "${RED}❌ 未知命令: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
