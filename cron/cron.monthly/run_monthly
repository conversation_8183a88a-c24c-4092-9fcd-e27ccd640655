#!/bin/sh

#清除缓存数据
rm -rf /data/InStock/instock/cache/hist/*
#MONTH=`date -d '' +%Y%m`
#cd /data/InStock/instock/cache/hist && rm -rf !(${MONTH})
#DATE=`date -d '' +%Y-%m-%d`
#DATE_1=`date -d '-1 days' +%Y-%m-%d`
#DATE_2=`date -d '-2 days' +%Y-%m-%d`
#cd /data/InStock/instock/cache/hist/${MONTH} && rm -rf !(${DATE}|${DATE_1}|${DATE_2})

#mkdir -p /data/logs
#DATE=`date +%Y-%m-%d:%H:%M:%S`
#echo $DATE >> /data/logs/monthly.log
