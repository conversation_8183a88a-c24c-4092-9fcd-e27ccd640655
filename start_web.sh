#!/bin/bash
# 启动Web服务的便捷脚本（使用venv环境）

# 检查是否在项目根目录
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "❌ 错误：虚拟环境不存在，请先运行 python3 -m venv venv"
    exit 1
fi

echo "🚀 启动股票分析Web服务..."

# 激活虚拟环境
source venv/bin/activate

# 检查依赖是否安装
python -c "import tornado, pandas, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  警告：依赖包未完全安装，正在安装..."
    pip install -r requirements.txt
fi

echo "✅ 虚拟环境已激活"
echo "🌐 启动Web服务..."

# 启动web服务
python instock/web/web_service.py
