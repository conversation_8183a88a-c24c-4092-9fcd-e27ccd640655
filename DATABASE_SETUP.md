# 📊 数据库配置指南

## 🎯 概述

本项目需要 MySQL 数据库来存储股票数据。以下是完整的数据库配置指南。

## 📋 前置条件

1. **MySQL 服务器** 已安装并运行
2. **数据库用户** 具有创建数据库的权限

## 🔧 数据库配置

### 当前默认配置

项目默认使用以下数据库配置（在 `instock/lib/database.py` 中）：

```python
db_host = "localhost"        # 数据库服务主机
db_user = "root"            # 数据库访问用户
db_password = "root"        # 数据库访问密码
db_database = "instockdb"   # 数据库名称
db_port = 3306             # 数据库服务端口
db_charset = "utf8mb4"     # 数据库字符集
```

### 方法1：使用环境变量（推荐）

您可以通过环境变量覆盖默认配置，而无需修改代码：

```bash
# 设置数据库配置环境变量
export db_host="localhost"
export db_user="your_username"
export db_password="your_password"
export db_database="instockdb"
export db_port="3306"

# 然后运行项目
./manage.sh job init
```

### 方法2：修改配置文件

直接编辑 `instock/lib/database.py` 文件中的配置：

```python
db_host = "your_host"           # 您的数据库主机
db_user = "your_username"       # 您的数据库用户名
db_password = "your_password"   # 您的数据库密码
db_database = "instockdb"       # 数据库名称（建议保持不变）
db_port = 3306                  # 数据库端口
```

## 🚀 MySQL 安装和配置

### macOS (使用 Homebrew)

```bash
# 安装 MySQL
brew install mysql

# 启动 MySQL 服务
brew services start mysql

# 安全配置（设置 root 密码等）
mysql_secure_installation

# 连接到 MySQL
mysql -u root -p
```

### 创建数据库用户（可选）

如果您想创建专门的用户而不是使用 root：

```sql
-- 连接到 MySQL
mysql -u root -p

-- 创建数据库用户
CREATE USER 'stockuser'@'localhost' IDENTIFIED BY 'your_password';

-- 授予权限
GRANT ALL PRIVILEGES ON *.* TO 'stockuser'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

## 🧪 测试数据库连接

使用项目提供的测试脚本：

```bash
# 测试数据库连接
./test_database.sh
```

或者手动测试：

```bash
# 激活虚拟环境并测试
source venv/bin/activate
python -c "
import instock.lib.database as db
try:
    conn = db.get_connection()
    if conn:
        print('✅ 数据库连接成功')
        conn.close()
    else:
        print('❌ 数据库连接失败')
except Exception as e:
    print(f'❌ 数据库连接错误: {e}')
"
```

## 🔄 初始化数据库

连接配置正确后，运行数据库初始化：

```bash
# 初始化数据库（创建数据库和表结构）
./manage.sh job init
```

## 🛠️ 故障排除

### 常见问题

1. **连接被拒绝 (Connection refused)**
   - 检查 MySQL 服务是否运行：`brew services list | grep mysql`
   - 启动 MySQL：`brew services start mysql`

2. **访问被拒绝 (Access denied)**
   - 检查用户名和密码是否正确
   - 确认用户具有相应权限

3. **数据库不存在**
   - 项目会自动创建数据库，确保用户有创建数据库的权限

4. **端口问题**
   - 确认 MySQL 运行在正确的端口（默认 3306）
   - 检查防火墙设置

### 检查 MySQL 状态

```bash
# 检查 MySQL 服务状态
brew services list | grep mysql

# 查看 MySQL 进程
ps aux | grep mysql

# 检查端口占用
lsof -i :3306
```

### 重置 MySQL 密码

如果忘记了 root 密码：

```bash
# 停止 MySQL
brew services stop mysql

# 安全模式启动
mysqld_safe --skip-grant-tables &

# 连接并重置密码
mysql -u root
USE mysql;
UPDATE user SET authentication_string=PASSWORD('new_password') WHERE User='root';
FLUSH PRIVILEGES;
EXIT;

# 重启 MySQL
brew services restart mysql
```

## 📝 配置示例

### 本地开发环境

```bash
export db_host="localhost"
export db_user="root"
export db_password="your_root_password"
export db_database="instockdb"
export db_port="3306"
```

### Docker 环境

```bash
export db_host="mysql_container"
export db_user="stockuser"
export db_password="stockpassword"
export db_database="instockdb"
export db_port="3306"
```

## ✅ 验证配置

成功配置后，您应该能够：

1. 连接到 MySQL 数据库
2. 运行 `./manage.sh job init` 创建数据库
3. 看到 "数据库创建成功" 的消息

如果遇到问题，请检查：
- MySQL 服务是否运行
- 用户名和密码是否正确
- 用户是否有足够的权限
- 网络连接是否正常
