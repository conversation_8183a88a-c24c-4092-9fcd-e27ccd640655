# 🚀 快速启动指南

## 📋 前置条件

1. **Python 3.9+** 已安装
2. **MySQL数据库** 已安装并运行
3. **虚拟环境** 已创建（如果没有，运行 `python3 -m venv venv`）
4. **TA-Lib** 系统库已安装（`brew install ta-lib`）

## 🔧 首次配置

### 1. 测试数据库连接
```bash
./manage.sh test-db
```

如果连接失败，请配置数据库：

**方法1：使用环境变量（推荐）**
```bash
export db_host="localhost"
export db_user="your_username"
export db_password="your_password"
export db_database="instockdb"
```

**方法2：修改配置文件**
编辑 `instock/lib/database.py` 中的数据库配置

详细配置说明请查看：`cat DATABASE_SETUP.md`

## 🎯 一键启动

### 1. 查看所有可用命令
```bash
./manage.sh help
```

### 2. 启动Web服务
```bash
./manage.sh web
```
访问地址：http://localhost:9988/

### 3. 运行数据处理任务
```bash
# 完整数据任务
./manage.sh job

# 基础数据任务
./manage.sh job basic

# 数据库初始化
./manage.sh job init
```

### 4. 启动交易服务
```bash
./manage.sh trade
```

### 5. 查看服务状态
```bash
./manage.sh status
```

### 6. 停止所有服务
```bash
./manage.sh stop
```

## 🔧 配置说明

### 数据库配置
在运行任务前，请确保MySQL数据库配置正确：

1. 检查 `instock/lib/database.py` 中的数据库连接配置
2. 确保MySQL服务正在运行
3. 创建相应的数据库和用户权限

### 依赖安装
如果遇到依赖问题：
```bash
./manage.sh install
```

## 📁 项目服务说明

### Web服务 (端口9988)
- **功能**: 提供股票数据的Web界面
- **启动**: `./manage.sh web` 或 `./start_web.sh`
- **访问**: http://localhost:9988/

### 数据处理任务
- **功能**: 获取和处理股票数据
- **启动**: `./manage.sh job` 或 `./start_job.sh`
- **类型**: 
  - 基础数据任务
  - 选股数据任务
  - 指标数据任务
  - 策略数据任务

### 交易服务
- **功能**: 自动化交易执行
- **启动**: `./manage.sh trade` 或 `./start_trade.sh`
- **注意**: 需要配置券商接口

## 🛠️ 故障排除

### 常见问题

1. **虚拟环境未激活**
   ```bash
   source venv/bin/activate
   ```

2. **依赖包缺失**
   ```bash
   ./manage.sh install
   ```

3. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证数据库配置信息
   - 确认用户权限

4. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :9988
   
   # 停止服务
   ./manage.sh stop
   ```

### 日志查看
- Web服务日志: `instock/web/log/stock_web.log`
- 任务执行日志: `instock/job/log/stock_execute_job.log`

## 📚 更多信息

- 详细环境配置: 查看 `VENV_SETUP.md`
- 项目文档: 查看 `README.md`
- 虚拟环境管理: 查看 `VENV_SETUP.md`

## 🎉 成功启动标志

当看到以下信息时，表示服务启动成功：

**Web服务启动成功:**
```
服务已启动，web地址 : http://localhost:9988/
```

**任务执行成功:**
```
######## 完成任务, 使用时间: X 秒 #######
```

**交易服务启动成功:**
```
交易服务已启动
```
