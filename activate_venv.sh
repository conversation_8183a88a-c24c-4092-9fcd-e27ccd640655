#!/bin/bash
# 激活虚拟环境的便捷脚本

# 检查是否在项目根目录
if [ ! -f "requirements.txt" ]; then
    echo "错误：请在项目根目录运行此脚本"
    exit 1
fi

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "错误：虚拟环境不存在，请先运行 python3 -m venv venv"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

echo "✅ 虚拟环境已激活"
echo "Python 版本: $(python --version)"
echo "pip 版本: $(pip --version)"
echo ""
echo "要退出虚拟环境，请运行: deactivate"
echo ""

# 启动新的shell会话以保持激活状态
exec $SHELL
