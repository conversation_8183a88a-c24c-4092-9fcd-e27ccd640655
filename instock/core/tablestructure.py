#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from sqlalchemy import DATE, VARCHAR, FLOAT, BIGINT, SmallInteger, DATETIME
from sqlalchemy.dialects.mysql import BIT
import talib as tl
from instock.core.strategy import enter
from instock.core.strategy import turtle_trade
from instock.core.strategy import climax_limitdown
from instock.core.strategy import low_atr
from instock.core.strategy import backtrace_ma250
from instock.core.strategy import breakthrough_platform
from instock.core.strategy import parking_apron
from instock.core.strategy import low_backtrace_increase
from instock.core.strategy import keep_increasing
from instock.core.strategy import high_tight_flag

__author__ = 'myh '
__date__ = '2023/3/10 '

RATE_FIELDS_COUNT = 100  # N日收益率字段数目，即N值
_COLLATE = "utf8mb4_general_ci"

TABLE_CN_STOCK_ATTENTION = {'name': 'cn_stock_attention', 'cn': '我的关注',
                            'columns': {'datetime': {'type': DATETIME, 'cn': '日期', 'size': 0},
                                        'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60}}}

TABLE_CN_ETF_SPOT = {'name': 'cn_etf_spot', 'cn': '每日ETF数据',
                     'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                 'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                 'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 120},
                                 'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                 'change_rate': {'type': FLOAT, 'cn': '涨跌幅', 'size': 70},
                                 'ups_downs': {'type': FLOAT, 'cn': '涨跌额', 'size': 70},
                                 'volume': {'type': BIGINT, 'cn': '成交量', 'size': 90},
                                 'deal_amount': {'type': BIGINT, 'cn': '成交额', 'size': 100},
                                 'open_price': {'type': FLOAT, 'cn': '开盘价', 'size': 70},
                                 'high_price': {'type': FLOAT, 'cn': '最高价', 'size': 70},
                                 'low_price': {'type': FLOAT, 'cn': '最低价', 'size': 70},
                                 'pre_close_price': {'type': FLOAT, 'cn': '昨收', 'size': 70},
                                 'turnoverrate': {'type': FLOAT, 'cn': '换手率', 'size': 70},
                                 'total_market_cap': {'type': BIGINT, 'cn': '总市值', 'size': 120},
                                 'free_cap': {'type': BIGINT, 'cn': '流通市值', 'size': 120}}}

TABLE_CN_STOCK_SPOT = {'name': 'cn_stock_spot', 'cn': '每日股票数据',
                       'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                   'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                   'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                   'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                   'change_rate': {'type': FLOAT, 'cn': '涨跌幅', 'size': 70},
                                   'ups_downs': {'type': FLOAT, 'cn': '涨跌额', 'size': 70},
                                   'volume': {'type': BIGINT, 'cn': '成交量', 'size': 90},
                                   'deal_amount': {'type': BIGINT, 'cn': '成交额', 'size': 100},
                                   'amplitude': {'type': FLOAT, 'cn': '振幅', 'size': 70},
                                   'turnoverrate': {'type': FLOAT, 'cn': '换手率', 'size': 70},
                                   'volume_ratio': {'type': FLOAT, 'cn': '量比', 'size': 70},
                                   'open_price': {'type': FLOAT, 'cn': '今开', 'size': 70},
                                   'high_price': {'type': FLOAT, 'cn': '最高', 'size': 70},
                                   'low_price': {'type': FLOAT, 'cn': '最低', 'size': 70},
                                   'pre_close_price': {'type': FLOAT, 'cn': '昨收', 'size': 70},
                                   'speed_increase': {'type': FLOAT, 'cn': '涨速', 'size': 70},
                                   'speed_increase_5': {'type': FLOAT, 'cn': '5分钟涨跌', 'size': 70},
                                   'speed_increase_60': {'type': FLOAT, 'cn': '60日涨跌幅', 'size': 70},
                                   'speed_increase_all': {'type': FLOAT, 'cn': '年初至今涨跌幅', 'size': 70},
                                   'dtsyl': {'type': FLOAT, 'cn': '市盈率动', 'size': 70},
                                   'pe9': {'type': FLOAT, 'cn': '市盈率TTM', 'size': 70},
                                   'pe': {'type': FLOAT, 'cn': '市盈率静', 'size': 70},
                                   'pbnewmrq': {'type': FLOAT, 'cn': '市净率', 'size': 70},
                                   'basic_eps': {'type': FLOAT, 'cn': '每股收益', 'size': 70},
                                   'bvps': {'type': FLOAT, 'cn': '每股净资产', 'size': 70},
                                   'per_capital_reserve': {'type': FLOAT, 'cn': '每股公积金', 'size': 70},
                                   'per_unassign_profit': {'type': FLOAT, 'cn': '每股未分配利润', 'size': 70},
                                   'roe_weight': {'type': FLOAT, 'cn': '加权净资产收益率', 'size': 70},
                                   'sale_gpr': {'type': FLOAT, 'cn': '毛利率', 'size': 70},
                                   'debt_asset_ratio': {'type': FLOAT, 'cn': '资产负债率', 'size': 70},
                                   'total_operate_income': {'type': BIGINT, 'cn': '营业收入', 'size': 120},
                                   'toi_yoy_ratio': {'type': FLOAT, 'cn': '营业收入同比增长', 'size': 70},
                                   'parent_netprofit': {'type': BIGINT, 'cn': '归属净利润', 'size': 110},
                                   'netprofit_yoy_ratio': {'type': FLOAT, 'cn': '归属净利润同比增长', 'size': 70},
                                   'report_date': {'type': DATE, 'cn': '报告期', 'size': 110},
                                   'total_shares': {'type': BIGINT, 'cn': '总股本', 'size': 120},
                                   'free_shares': {'type': BIGINT, 'cn': '已流通股份', 'size': 120},
                                   'total_market_cap': {'type': BIGINT, 'cn': '总市值', 'size': 120},
                                   'free_cap': {'type': BIGINT, 'cn': '流通市值', 'size': 120},
                                   'industry': {'type': VARCHAR(20, _COLLATE), 'cn': '所处行业', 'size': 100},
                                   'listing_date': {'type': DATE, 'cn': '上市时间', 'size': 110}}}

TABLE_CN_STOCK_SPOT_BUY = {'name': 'cn_stock_spot_buy', 'cn': '基本面选股',
                           'columns': TABLE_CN_STOCK_SPOT['columns'].copy()}

CN_STOCK_FUND_FLOW = ({'name': 'stock_individual_fund_flow_rank', 'cn': '今日',
                       'columns': {'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                   'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                   'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                   'change_rate': {'type': FLOAT, 'cn': '今日涨跌幅', 'size': 70},
                                   'fund_amount': {'type': BIGINT, 'cn': '今日主力净流入-净额', 'size': 100},
                                   'fund_rate': {'type': FLOAT, 'cn': '今日主力净流入-净占比', 'size': 70},
                                   'fund_amount_super': {'type': BIGINT, 'cn': '今日超大单净流入-净额', 'size': 100},
                                   'fund_rate_super': {'type': FLOAT, 'cn': '今日超大单净流入-净占比', 'size': 70},
                                   'fund_amount_large': {'type': BIGINT, 'cn': '今日大单净流入-净额', 'size': 100},
                                   'fund_rate_large': {'type': FLOAT, 'cn': '今日大单净流入-净占比', 'size': 70},
                                   'fund_amount_medium': {'type': BIGINT, 'cn': '今日中单净流入-净额', 'size': 100},
                                   'fund_rate_medium': {'type': FLOAT, 'cn': '今日中单净流入-净占比', 'size': 70},
                                   'fund_amount_small': {'type': BIGINT, 'cn': '今日小单净流入-净额', 'size': 100},
                                   'fund_rate_small': {'type': FLOAT, 'cn': '今日小单净流入-净占比', 'size': 70}}},
                      {'name': 'stock_individual_fund_flow_rank', 'cn': '3日',
                       'columns': {'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                   'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                   'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                   'change_rate_3': {'type': FLOAT, 'cn': '3日涨跌幅', 'size': 70},
                                   'fund_amount_3': {'type': BIGINT, 'cn': '3日主力净流入-净额', 'size': 100},
                                   'fund_rate_3': {'type': FLOAT, 'cn': '3日主力净流入-净占比', 'size': 70},
                                   'fund_amount_super_3': {'type': BIGINT, 'cn': '3日超大单净流入-净额', 'size': 100},
                                   'fund_rate_super_3': {'type': FLOAT, 'cn': '3日超大单净流入-净占比', 'size': 70},
                                   'fund_amount_large_3': {'type': BIGINT, 'cn': '3日大单净流入-净额', 'size': 100},
                                   'fund_rate_large_3': {'type': FLOAT, 'cn': '3日大单净流入-净占比', 'size': 70},
                                   'fund_amount_medium_3': {'type': BIGINT, 'cn': '3日中单净流入-净额', 'size': 100},
                                   'fund_rate_medium_3': {'type': FLOAT, 'cn': '3日中单净流入-净占比', 'size': 70},
                                   'fund_amount_small_3': {'type': BIGINT, 'cn': '3日小单净流入-净额', 'size': 100},
                                   'fund_rate_small_3': {'type': FLOAT, 'cn': '3日小单净流入-净占比', 'size': 70}}},
                      {'name': 'stock_individual_fund_flow_rank', 'cn': '5日',
                       'columns': {'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                   'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                   'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                   'change_rate_5': {'type': FLOAT, 'cn': '5日涨跌幅', 'size': 70},
                                   'fund_amount_5': {'type': BIGINT, 'cn': '5日主力净流入-净额', 'size': 100},
                                   'fund_rate_5': {'type': FLOAT, 'cn': '5日主力净流入-净占比', 'size': 70},
                                   'fund_amount_super_5': {'type': BIGINT, 'cn': '5日超大单净流入-净额', 'size': 100},
                                   'fund_rate_super_5': {'type': FLOAT, 'cn': '5日超大单净流入-净占比', 'size': 70},
                                   'fund_amount_large_5': {'type': BIGINT, 'cn': '5日大单净流入-净额', 'size': 100},
                                   'fund_rate_large_5': {'type': FLOAT, 'cn': '5日大单净流入-净占比', 'size': 70},
                                   'fund_amount_medium_5': {'type': BIGINT, 'cn': '5日中单净流入-净额', 'size': 100},
                                   'fund_rate_medium_5': {'type': FLOAT, 'cn': '5日中单净流入-净占比', 'size': 70},
                                   'fund_amount_small_5': {'type': BIGINT, 'cn': '5日小单净流入-净额', 'size': 100},
                                   'fund_rate_small_5': {'type': FLOAT, 'cn': '5日小单净流入-净占比', 'size': 70}}},
                      {'name': 'stock_individual_fund_flow_rank', 'cn': '10日',
                       'columns': {'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                   'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                   'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                   'change_rate_10': {'type': FLOAT, 'cn': '10日涨跌幅', 'size': 70},
                                   'fund_amount_10': {'type': BIGINT, 'cn': '10日主力净流入-净额', 'size': 100},
                                   'fund_rate_10': {'type': FLOAT, 'cn': '10日主力净流入-净占比', 'size': 70},
                                   'fund_amount_super_10': {'type': BIGINT, 'cn': '10日超大单净流入-净额', 'size': 100},
                                   'fund_rate_super_10': {'type': FLOAT, 'cn': '10日超大单净流入-净占比', 'size': 70},
                                   'fund_amount_large_10': {'type': BIGINT, 'cn': '10日大单净流入-净额', 'size': 100},
                                   'fund_rate_large_10': {'type': FLOAT, 'cn': '10日大单净流入-净占比', 'size': 70},
                                   'fund_amount_medium_10': {'type': BIGINT, 'cn': '10日中单净流入-净额', 'size': 100},
                                   'fund_rate_medium_10': {'type': FLOAT, 'cn': '10日中单净流入-净占比', 'size': 70},
                                   'fund_amount_small_10': {'type': BIGINT, 'cn': '10日小单净流入-净额', 'size': 100},
                                   'fund_rate_small_10': {'type': FLOAT, 'cn': '10日小单净流入-净占比', 'size': 70}}})

TABLE_CN_STOCK_FUND_FLOW = {'name': 'cn_stock_fund_flow', 'cn': '股票资金流向',
                            'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0}}}
for cf in CN_STOCK_FUND_FLOW:
    TABLE_CN_STOCK_FUND_FLOW['columns'].update(cf['columns'].copy())

CN_STOCK_SECTOR_FUND_FLOW = (('行业资金流', '概念资金流'),
                             ({'name': 'stock_sector_fund_flow_rank', 'cn': '今日',
                              'columns': {'name': {'type': VARCHAR(30, _COLLATE), 'cn': '名称', 'size': 70},
                                          'change_rate': {'type': FLOAT, 'cn': '今日涨跌幅', 'size': 70},
                                          'fund_amount': {'type': BIGINT, 'cn': '今日主力净流入-净额', 'size': 100},
                                          'fund_rate': {'type': FLOAT, 'cn': '今日主力净流入-净占比', 'size': 70},
                                          'fund_amount_super': {'type': BIGINT, 'cn': '今日超大单净流入-净额', 'size': 100},
                                          'fund_rate_super': {'type': FLOAT, 'cn': '今日超大单净流入-净占比', 'size': 70},
                                          'fund_amount_large': {'type': BIGINT, 'cn': '今日大单净流入-净额', 'size': 100},
                                          'fund_rate_large': {'type': FLOAT, 'cn': '今日大单净流入-净占比', 'size': 70},
                                          'fund_amount_medium': {'type': BIGINT, 'cn': '今日中单净流入-净额', 'size': 100},
                                          'fund_rate_medium': {'type': FLOAT, 'cn': '今日中单净流入-净占比', 'size': 70},
                                          'fund_amount_small': {'type': BIGINT, 'cn': '今日小单净流入-净额', 'size': 100},
                                          'fund_rate_small': {'type': FLOAT, 'cn': '今日小单净流入-净占比', 'size': 70},
                                          'stock_name': {'type': VARCHAR(20, _COLLATE), 'cn': '今日主力净流入最大股', 'size': 70}}},
                             {'name': 'stock_individual_fund_flow_rank', 'cn': '5日',
                              'columns': {'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                          'change_rate_5': {'type': FLOAT, 'cn': '5日涨跌幅', 'size': 70},
                                          'fund_amount_5': {'type': BIGINT, 'cn': '5日主力净流入-净额', 'size': 100},
                                          'fund_rate_5': {'type': FLOAT, 'cn': '5日主力净流入-净占比', 'size': 70},
                                          'fund_amount_super_5': {'type': BIGINT, 'cn': '5日超大单净流入-净额', 'size': 100},
                                          'fund_rate_super_5': {'type': FLOAT, 'cn': '5日超大单净流入-净占比', 'size': 70},
                                          'fund_amount_large_5': {'type': BIGINT, 'cn': '5日大单净流入-净额', 'size': 100},
                                          'fund_rate_large_5': {'type': FLOAT, 'cn': '5日大单净流入-净占比', 'size': 70},
                                          'fund_amount_medium_5': {'type': BIGINT, 'cn': '5日中单净流入-净额', 'size': 100},
                                          'fund_rate_medium_5': {'type': FLOAT, 'cn': '5日中单净流入-净占比', 'size': 70},
                                          'fund_amount_small_5': {'type': BIGINT, 'cn': '5日小单净流入-净额', 'size': 100},
                                          'fund_rate_small_5': {'type': FLOAT, 'cn': '5日小单净流入-净占比', 'size': 70},
                                          'stock_name_5': {'type': VARCHAR(20, _COLLATE), 'cn': '5日主力净流入最大股', 'size': 70}}},
                             {'name': 'stock_individual_fund_flow_rank', 'cn': '10日',
                              'columns': {'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                          'change_rate_10': {'type': FLOAT, 'cn': '10日涨跌幅', 'size': 70},
                                          'fund_amount_10': {'type': BIGINT, 'cn': '10日主力净流入-净额', 'size': 100},
                                          'fund_rate_10': {'type': FLOAT, 'cn': '10日主力净流入-净占比', 'size': 70},
                                          'fund_amount_super_10': {'type': BIGINT, 'cn': '10日超大单净流入-净额', 'size': 100},
                                          'fund_rate_super_10': {'type': FLOAT, 'cn': '10日超大单净流入-净占比', 'size': 70},
                                          'fund_amount_large_10': {'type': BIGINT, 'cn': '10日大单净流入-净额', 'size': 100},
                                          'fund_rate_large_10': {'type': FLOAT, 'cn': '10日大单净流入-净占比', 'size': 70},
                                          'fund_amount_medium_10': {'type': BIGINT, 'cn': '10日中单净流入-净额', 'size': 100},
                                          'fund_rate_medium_10': {'type': FLOAT, 'cn': '10日中单净流入-净占比', 'size': 70},
                                          'fund_amount_small_10': {'type': BIGINT, 'cn': '10日小单净流入-净额', 'size': 100},
                                          'fund_rate_small_10': {'type': FLOAT, 'cn': '10日小单净流入-净占比', 'size': 70},
                                          'stock_name_10': {'type': VARCHAR(20, _COLLATE), 'cn': '10日主力净流入最大股', 'size': 70}}}))

TABLE_CN_STOCK_FUND_FLOW_INDUSTRY = {'name': 'cn_stock_fund_flow_industry', 'cn': '行业资金流向',
                                     'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0}}}
for cf in CN_STOCK_SECTOR_FUND_FLOW[1]:
    TABLE_CN_STOCK_FUND_FLOW_INDUSTRY['columns'].update(cf['columns'].copy())

TABLE_CN_STOCK_FUND_FLOW_CONCEPT = {'name': 'cn_stock_fund_flow_concept', 'cn': '概念资金流向',
                                    'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0}}}
for cf in CN_STOCK_SECTOR_FUND_FLOW[1]:
    TABLE_CN_STOCK_FUND_FLOW_CONCEPT['columns'].update(cf['columns'].copy())

TABLE_CN_STOCK_BONUS = {'name': 'cn_stock_bonus', 'cn': '股票分红配送',
                        'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                    'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                    'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                    'convertible_total_rate': {'type': FLOAT, 'cn': '送转股份-送转总比例', 'size': 70},
                                    'convertible_rate': {'type': FLOAT, 'cn': '送转股份-送转比例', 'size': 70},
                                    'convertible_transfer_rate': {'type': FLOAT, 'cn': '送转股份-转股比例', 'size': 70},
                                    'bonusaward_rate': {'type': FLOAT, 'cn': '现金分红-现金分红比例', 'size': 70},
                                    'bonusaward_yield': {'type': FLOAT, 'cn': '现金分红-股息率', 'size': 70},
                                    'basic_eps': {'type': FLOAT, 'cn': '每股收益', 'size': 70},
                                    'bvps': {'type': FLOAT, 'cn': '每股净资产', 'size': 70},
                                    'per_capital_reserve': {'type': FLOAT, 'cn': '每股公积金', 'size': 70},
                                    'per_unassign_profit': {'type': FLOAT, 'cn': '每股未分配利润', 'size': 70},
                                    'netprofit_yoy_ratio': {'type': FLOAT, 'cn': '净利润同比增长', 'size': 70},
                                    'total_shares': {'type': BIGINT, 'cn': '总股本', 'size': 120},
                                    'plan_date': {'type': DATE, 'cn': '预案公告日', 'size': 110},
                                    'record_date': {'type': DATE, 'cn': '股权登记日', 'size': 110},
                                    'ex_dividend_date': {'type': DATE, 'cn': '除权除息日', 'size': 110},
                                    'progress': {'type': VARCHAR(50, _COLLATE), 'cn': '方案进度', 'size': 100},
                                    'report_date': {'type': DATE, 'cn': '最新公告日期', 'size': 110}}}

TABLE_CN_STOCK_TOP = {'name': 'cn_stock_top', 'cn': '股票龙虎榜',
                      'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                  'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                  'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                  'ranking_times': {'type': FLOAT, 'cn': '上榜次数', 'size': 70},
                                  'sum_buy': {'type': FLOAT, 'cn': '累积购买额', 'size': 100},
                                  'sum_sell': {'type': FLOAT, 'cn': '累积卖出额', 'size': 100},
                                  'net_amount': {'type': FLOAT, 'cn': '净额', 'size': 100},
                                  'buy_seat': {'type': FLOAT, 'cn': '买入席位数', 'size': 100},
                                  'sell_seat': {'type': FLOAT, 'cn': '卖出席位数', 'size': 100}}}

TABLE_CN_STOCK_BLOCKTRADE = {'name': 'cn_stock_blocktrade', 'cn': '股票大宗交易',
                             'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                         'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                         'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70},
                                         'new_price': {'type': FLOAT, 'cn': '收盘价', 'size': 70},
                                         'change_rate': {'type': FLOAT, 'cn': '涨跌幅', 'size': 70},
                                         'average_price': {'type': FLOAT, 'cn': '成交均价', 'size': 70},
                                         'overflow_rate': {'type': FLOAT, 'cn': '折溢率', 'size': 120},
                                         'trade_number': {'type': FLOAT, 'cn': '成交笔数', 'size': 70},
                                         'sum_volume': {'type': FLOAT, 'cn': '成交总量', 'size': 100},
                                         'sum_turnover': {'type': FLOAT, 'cn': '成交总额', 'size': 100},
                                         'turnover_market_rate': {'type': FLOAT, 'cn': '成交占比流通市值',
                                                                  'size': 120}}}

CN_STOCK_HIST_DATA = {'name': 'fund_etf_hist_em', 'cn': '基金某时间段的日行情数据库',
                      'columns': {'date': {'type': DATE, 'cn': '日期'},
                                  'open': {'type': FLOAT, 'cn': '开盘'},
                                  'close': {'type': FLOAT, 'cn': '收盘'},
                                  'high': {'type': FLOAT, 'cn': '最高'},
                                  'low': {'type': FLOAT, 'cn': '最低'},
                                  'volume': {'type': FLOAT, 'cn': '成交量'},
                                  'amount': {'type': FLOAT, 'cn': '成交额'},
                                  'amplitude': {'type': FLOAT, 'cn': '振幅'},
                                  'quote_change': {'type': FLOAT, 'cn': '涨跌幅'},
                                  'ups_downs': {'type': FLOAT, 'cn': '涨跌额'},
                                  'turnover': {'type': FLOAT, 'cn': '换手率'}}}

TABLE_CN_STOCK_FOREIGN_KEY = {'name': 'cn_stock_foreign_key', 'cn': '股票外键',
                              'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                          'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                          'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70}}}

TABLE_CN_STOCK_BACKTEST_DATA = {'name': 'cn_stock_backtest_data', 'cn': '股票回归测试数据',
                                'columns': {'rate_%s' % i: {'type': FLOAT, 'cn': '%s日收益率' % i, 'size': 100} for i in
                                            range(1, RATE_FIELDS_COUNT + 1, 1)}}

STOCK_STATS_DATA = {'name': 'calculate_indicator', 'cn': '股票统计/指标计算助手库',
                    'columns': {'close': {'type': FLOAT, 'cn': '价格', 'size': 0},
                                'macd': {'type': FLOAT, 'cn': 'dif', 'size': 70},
                                'macds': {'type': FLOAT, 'cn': 'macd', 'size': 70},
                                'macdh': {'type': FLOAT, 'cn': 'histogram', 'size': 70},
                                'kdjk': {'type': FLOAT, 'cn': 'kdjk', 'size': 70},
                                'kdjd': {'type': FLOAT, 'cn': 'kdjd', 'size': 70},
                                'kdjj': {'type': FLOAT, 'cn': 'kdjj', 'size': 70},
                                'boll_ub': {'type': FLOAT, 'cn': 'boll上轨', 'size': 70},
                                'boll': {'type': FLOAT, 'cn': 'boll', 'size': 70},
                                'boll_lb': {'type': FLOAT, 'cn': 'boll下轨', 'size': 70},
                                'trix': {'type': FLOAT, 'cn': 'trix', 'size': 70},
                                'trix_20_sma': {'type': FLOAT, 'cn': 'trma', 'size': 70},
                                'tema': {'type': FLOAT, 'cn': 'tema', 'size': 70},
                                'cr': {'type': FLOAT, 'cn': 'cr', 'size': 70},
                                'cr-ma1': {'type': FLOAT, 'cn': 'cr-ma1', 'size': 70},
                                'cr-ma2': {'type': FLOAT, 'cn': 'cr-ma2', 'size': 70},
                                'cr-ma3': {'type': FLOAT, 'cn': 'cr-ma3', 'size': 70},
                                'rsi_6': {'type': FLOAT, 'cn': 'rsi_6', 'size': 70},
                                'rsi_12': {'type': FLOAT, 'cn': 'rsi_12', 'size': 70},
                                'rsi': {'type': FLOAT, 'cn': 'rsi', 'size': 70},
                                'rsi_24': {'type': FLOAT, 'cn': 'rsi_24', 'size': 70},
                                'vr': {'type': FLOAT, 'cn': 'vr', 'size': 70},
                                'vr_6_sma': {'type': FLOAT, 'cn': 'mavr', 'size': 70},
                                'roc': {'type': FLOAT, 'cn': 'roc', 'size': 70},
                                'rocma': {'type': FLOAT, 'cn': 'rocma', 'size': 70},
                                'rocema': {'type': FLOAT, 'cn': 'rocema', 'size': 70},
                                'pdi': {'type': FLOAT, 'cn': 'pdi', 'size': 70},
                                'mdi': {'type': FLOAT, 'cn': 'mdi', 'size': 70},
                                'dx': {'type': FLOAT, 'cn': 'dx', 'size': 70},
                                'adx': {'type': FLOAT, 'cn': 'adx', 'size': 70},
                                'adxr': {'type': FLOAT, 'cn': 'adxr', 'size': 70},
                                'wr_6': {'type': FLOAT, 'cn': 'wr_6', 'size': 70},
                                'wr_10': {'type': FLOAT, 'cn': 'wr_10', 'size': 70},
                                'wr_14': {'type': FLOAT, 'cn': 'wr_14', 'size': 70},
                                'cci': {'type': FLOAT, 'cn': 'cci', 'size': 70},
                                'cci_84': {'type': FLOAT, 'cn': 'cci_84', 'size': 70},
                                'tr': {'type': FLOAT, 'cn': 'tr', 'size': 70},
                                'atr': {'type': FLOAT, 'cn': 'atr', 'size': 70},
                                'dma': {'type': FLOAT, 'cn': 'dma', 'size': 70},
                                'dma_10_sma': {'type': FLOAT, 'cn': 'ama', 'size': 70},
                                'obv': {'type': FLOAT, 'cn': 'obv', 'size': 70},
                                'sar': {'type': FLOAT, 'cn': 'sar', 'size': 70},
                                'psy': {'type': FLOAT, 'cn': 'psy', 'size': 70},
                                'psyma': {'type': FLOAT, 'cn': 'psyma', 'size': 70},
                                'br': {'type': FLOAT, 'cn': 'br', 'size': 70},
                                'ar': {'type': FLOAT, 'cn': 'ar', 'size': 70},
                                'emv': {'type': FLOAT, 'cn': 'emv', 'size': 70},
                                'emva': {'type': FLOAT, 'cn': 'emva', 'size': 70},
                                'bias': {'type': FLOAT, 'cn': 'bias', 'size': 70},
                                'mfi': {'type': FLOAT, 'cn': 'mfi', 'size': 70},
                                'mfisma': {'type': FLOAT, 'cn': 'mfisma', 'size': 70},
                                'vwma': {'type': FLOAT, 'cn': 'vwma', 'size': 70},
                                'mvwma': {'type': FLOAT, 'cn': 'mvwma', 'size': 70},
                                'ppo': {'type': FLOAT, 'cn': 'ppo', 'size': 70},
                                'ppos': {'type': FLOAT, 'cn': 'ppos', 'size': 70},
                                'ppoh': {'type': FLOAT, 'cn': 'ppoh', 'size': 70},
                                'wt1': {'type': FLOAT, 'cn': 'wt1', 'size': 70},
                                'wt2': {'type': FLOAT, 'cn': 'wt2', 'size': 70},
                                'supertrend_ub': {'type': FLOAT, 'cn': 'supertrend_ub', 'size': 70},
                                'supertrend': {'type': FLOAT, 'cn': 'supertrend', 'size': 70},
                                'supertrend_lb': {'type': FLOAT, 'cn': 'supertrend_lb', 'size': 70},
                                'dpo': {'type': FLOAT, 'cn': 'dpo', 'size': 70},
                                'madpo': {'type': FLOAT, 'cn': 'madpo', 'size': 70},
                                'vhf': {'type': FLOAT, 'cn': 'vhf', 'size': 70},
                                'rvi': {'type': FLOAT, 'cn': 'rvi', 'size': 70},
                                'rvis': {'type': FLOAT, 'cn': 'rvis', 'size': 70},
                                'fi': {'type': FLOAT, 'cn': 'fi', 'size': 70},
                                'force_2': {'type': FLOAT, 'cn': 'force_2', 'size': 70},
                                'force_13': {'type': FLOAT, 'cn': 'force_13', 'size': 70},
                                'ene_ue': {'type': FLOAT, 'cn': 'ene上轨', 'size': 70},
                                'ene': {'type': FLOAT, 'cn': 'ene', 'size': 70},
                                'ene_le': {'type': FLOAT, 'cn': 'ene下轨', 'size': 70},
                                'stochrsi_k': {'type': FLOAT, 'cn': 'stochrsi_k', 'size': 70},
                                'stochrsi_d': {'type': FLOAT, 'cn': 'stochrsi_d', 'size': 70}}}

TABLE_CN_STOCK_INDICATORS = {'name': 'cn_stock_indicators', 'cn': '股票指标数据',
                             'columns': TABLE_CN_STOCK_FOREIGN_KEY['columns'].copy()}
TABLE_CN_STOCK_INDICATORS['columns'].update(STOCK_STATS_DATA['columns'])

_tmp_columns = TABLE_CN_STOCK_FOREIGN_KEY['columns'].copy()
_tmp_columns.update(TABLE_CN_STOCK_BACKTEST_DATA['columns'])

TABLE_CN_STOCK_INDICATORS_BUY = {'name': 'cn_stock_indicators_buy', 'cn': '股票指标买入',
                                 'columns': _tmp_columns}

TABLE_CN_STOCK_INDICATORS_SELL = {'name': 'cn_stock_indicators_sell', 'cn': '股票指标卖出',
                                  'columns': _tmp_columns}

TABLE_CN_STOCK_STRATEGIES = [
    {'name': 'cn_stock_strategy_enter', 'cn': '放量上涨', 'size': 70, 'func': enter.check_volume,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_keep_increasing', 'cn': '均线多头', 'size': 70, 'func': keep_increasing.check,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_parking_apron', 'cn': '停机坪', 'size': 70, 'func': parking_apron.check,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_backtrace_ma250', 'cn': '回踩年线', 'size': 70, 'func': backtrace_ma250.check,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_breakthrough_platform', 'cn': '突破平台', 'size': 70,
     'func': breakthrough_platform.check,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_low_backtrace_increase', 'cn': '无大幅回撤', 'size': 70,
     'func': low_backtrace_increase.check,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_turtle_trade', 'cn': '海龟交易法则', 'size': 70, 'func': turtle_trade.check_enter,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_high_tight_flag', 'cn': '高而窄的旗形', 'size': 70,
     'func': high_tight_flag.check_high_tight,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_climax_limitdown', 'cn': '放量跌停', 'size': 70, 'func': climax_limitdown.check,
     'columns': _tmp_columns},
    {'name': 'cn_stock_strategy_low_atr', 'cn': '低ATR成长', 'size': 70, 'func': low_atr.check_low_increase,
     'columns': _tmp_columns}
]

STOCK_KLINE_PATTERN_DATA = {'name': 'cn_stock_pattern_recognitions', 'cn': 'K线形态',
                            'columns': {
                                'tow_crows': {'type': SmallInteger, 'cn': '两只乌鸦', 'size': 70, 'func': tl.CDL2CROWS},
                                'upside_gap_two_crows': {'type': SmallInteger, 'cn': '向上跳空的两只乌鸦', 'size': 70,
                                                         'func': tl.CDLUPSIDEGAP2CROWS},
                                'three_black_crows': {'type': SmallInteger, 'cn': '三只乌鸦', 'size': 70,
                                                      'func': tl.CDL3BLACKCROWS},
                                'identical_three_crows': {'type': SmallInteger, 'cn': '三胞胎乌鸦', 'size': 70,
                                                          'func': tl.CDLIDENTICAL3CROWS},
                                'three_line_strike': {'type': SmallInteger, 'cn': '三线打击', 'size': 70,
                                                      'func': tl.CDL3LINESTRIKE},
                                'dark_cloud_cover': {'type': SmallInteger, 'cn': '乌云压顶', 'size': 70,
                                                     'func': tl.CDLDARKCLOUDCOVER},
                                'evening_doji_star': {'type': SmallInteger, 'cn': '十字暮星', 'size': 70,
                                                      'func': tl.CDLEVENINGDOJISTAR},
                                'doji_Star': {'type': SmallInteger, 'cn': '十字星', 'size': 70, 'func': tl.CDLDOJISTAR},
                                'hanging_man': {'type': SmallInteger, 'cn': '上吊线', 'size': 70,
                                                'func': tl.CDLHANGINGMAN},
                                'hikkake_pattern': {'type': SmallInteger, 'cn': '陷阱', 'size': 70,
                                                    'func': tl.CDLHIKKAKE},
                                'modified_hikkake_pattern': {'type': SmallInteger, 'cn': '修正陷阱', 'size': 70,
                                                             'func': tl.CDLHIKKAKEMOD},
                                'in_neck_pattern': {'type': SmallInteger, 'cn': '颈内线', 'size': 70,
                                                    'func': tl.CDLINNECK},
                                'on_neck_pattern': {'type': SmallInteger, 'cn': '颈上线', 'size': 70,
                                                    'func': tl.CDLONNECK},
                                'thrusting_pattern': {'type': SmallInteger, 'cn': '插入', 'size': 70,
                                                      'func': tl.CDLTHRUSTING},
                                'shooting_star': {'type': SmallInteger, 'cn': '射击之星', 'size': 70,
                                                  'func': tl.CDLSHOOTINGSTAR},
                                'stalled_pattern': {'type': SmallInteger, 'cn': '停顿形态', 'size': 70,
                                                    'func': tl.CDLSTALLEDPATTERN},
                                'advance_block': {'type': SmallInteger, 'cn': '大敌当前', 'size': 70,
                                                  'func': tl.CDLADVANCEBLOCK},
                                'high_wave_candle': {'type': SmallInteger, 'cn': '风高浪大线', 'size': 70,
                                                     'func': tl.CDLHIGHWAVE},
                                'engulfing_pattern': {'type': SmallInteger, 'cn': '吞噬模式', 'size': 70,
                                                      'func': tl.CDLENGULFING},
                                'abandoned_baby': {'type': SmallInteger, 'cn': '弃婴', 'size': 70,
                                                   'func': tl.CDLABANDONEDBABY},
                                'closing_marubozu': {'type': SmallInteger, 'cn': '收盘缺影线', 'size': 70,
                                                     'func': tl.CDLCLOSINGMARUBOZU},
                                'doji': {'type': SmallInteger, 'cn': '十字', 'size': 70, 'func': tl.CDLDOJI},
                                'up_down_gap': {'type': SmallInteger, 'cn': '向上/下跳空并列阳线', 'size': 70,
                                                'func': tl.CDLGAPSIDESIDEWHITE},
                                'long_legged_doji': {'type': SmallInteger, 'cn': '长脚十字', 'size': 70,
                                                     'func': tl.CDLLONGLEGGEDDOJI},
                                'rickshaw_man': {'type': SmallInteger, 'cn': '黄包车夫', 'size': 70,
                                                 'func': tl.CDLRICKSHAWMAN},
                                'marubozu': {'type': SmallInteger, 'cn': '光头光脚/缺影线', 'size': 70,
                                             'func': tl.CDLMARUBOZU},
                                'three_inside_up_down': {'type': SmallInteger, 'cn': '三内部上涨和下跌', 'size': 70,
                                                         'func': tl.CDL3INSIDE},
                                'three_outside_up_down': {'type': SmallInteger, 'cn': '三外部上涨和下跌', 'size': 70,
                                                          'func': tl.CDL3OUTSIDE},
                                'three_stars_in_the_south': {'type': SmallInteger, 'cn': '南方三星', 'size': 70,
                                                             'func': tl.CDL3STARSINSOUTH},
                                'three_white_soldiers': {'type': SmallInteger, 'cn': '三个白兵', 'size': 70,
                                                         'func': tl.CDL3WHITESOLDIERS},
                                'belt_hold': {'type': SmallInteger, 'cn': '捉腰带线', 'size': 70,
                                              'func': tl.CDLBELTHOLD},
                                'breakaway': {'type': SmallInteger, 'cn': '脱离', 'size': 70, 'func': tl.CDLBREAKAWAY},
                                'concealing_baby_swallow': {'type': SmallInteger, 'cn': '藏婴吞没', 'size': 70,
                                                            'func': tl.CDLCONCEALBABYSWALL},
                                'counterattack': {'type': SmallInteger, 'cn': '反击线', 'size': 70,
                                                  'func': tl.CDLCOUNTERATTACK},
                                'dragonfly_doji': {'type': SmallInteger, 'cn': '蜻蜓十字/T形十字', 'size': 70,
                                                   'func': tl.CDLDRAGONFLYDOJI},
                                'evening_star': {'type': SmallInteger, 'cn': '暮星', 'size': 70,
                                                 'func': tl.CDLEVENINGSTAR},
                                'gravestone_doji': {'type': SmallInteger, 'cn': '墓碑十字/倒T十字', 'size': 70,
                                                    'func': tl.CDLGRAVESTONEDOJI},
                                'hammer': {'type': SmallInteger, 'cn': '锤头', 'size': 70, 'func': tl.CDLHAMMER},
                                'harami_pattern': {'type': SmallInteger, 'cn': '母子线', 'size': 70,
                                                   'func': tl.CDLHARAMI},
                                'harami_cross_pattern': {'type': SmallInteger, 'cn': '十字孕线', 'size': 70,
                                                         'func': tl.CDLHARAMICROSS},
                                'homing_pigeon': {'type': SmallInteger, 'cn': '家鸽', 'size': 70,
                                                  'func': tl.CDLHOMINGPIGEON},
                                'inverted_hammer': {'type': SmallInteger, 'cn': '倒锤头', 'size': 70,
                                                    'func': tl.CDLINVERTEDHAMMER},
                                'kicking': {'type': SmallInteger, 'cn': '反冲形态', 'size': 70, 'func': tl.CDLKICKING},
                                'kicking_bull_bear': {'type': SmallInteger, 'cn': '由较长缺影线决定的反冲形态',
                                                      'size': 70,
                                                      'func': tl.CDLKICKINGBYLENGTH},
                                'ladder_bottom': {'type': SmallInteger, 'cn': '梯底', 'size': 70,
                                                  'func': tl.CDLLADDERBOTTOM},
                                'long_line_candle': {'type': SmallInteger, 'cn': '长蜡烛', 'size': 70,
                                                     'func': tl.CDLLONGLINE},
                                'matching_low': {'type': SmallInteger, 'cn': '相同低价', 'size': 70,
                                                 'func': tl.CDLMATCHINGLOW},
                                'mat_hold': {'type': SmallInteger, 'cn': '铺垫', 'size': 70, 'func': tl.CDLMATHOLD},
                                'morning_doji_star': {'type': SmallInteger, 'cn': '十字晨星', 'size': 70,
                                                      'func': tl.CDLMORNINGDOJISTAR},
                                'morning_star': {'type': SmallInteger, 'cn': '晨星', 'size': 70,
                                                 'func': tl.CDLMORNINGSTAR},
                                'piercing_pattern': {'type': SmallInteger, 'cn': '刺透形态', 'size': 70,
                                                     'func': tl.CDLPIERCING},
                                'rising_falling_three': {'type': SmallInteger, 'cn': '上升/下降三法', 'size': 70,
                                                         'func': tl.CDLRISEFALL3METHODS},
                                'separating_lines': {'type': SmallInteger, 'cn': '分离线', 'size': 70,
                                                     'func': tl.CDLSEPARATINGLINES},
                                'short_line_candle': {'type': SmallInteger, 'cn': '短蜡烛', 'size': 70,
                                                      'func': tl.CDLSHORTLINE},
                                'spinning_top': {'type': SmallInteger, 'cn': '纺锤', 'size': 70,
                                                 'func': tl.CDLSPINNINGTOP},
                                'stick_sandwich': {'type': SmallInteger, 'cn': '条形三明治', 'size': 70,
                                                   'func': tl.CDLSTICKSANDWICH},
                                'takuri': {'type': SmallInteger, 'cn': '探水竿', 'size': 70, 'func': tl.CDLTAKURI},
                                'tasuki_gap': {'type': SmallInteger, 'cn': '跳空并列阴阳线', 'size': 70,
                                               'func': tl.CDLTASUKIGAP},
                                'tristar_pattern': {'type': SmallInteger, 'cn': '三星', 'size': 70,
                                                    'func': tl.CDLTRISTAR},
                                'unique_3_river': {'type': SmallInteger, 'cn': '奇特三河床', 'size': 70,
                                                   'func': tl.CDLUNIQUE3RIVER},
                                'upside_downside_gap': {'type': SmallInteger, 'cn': '上升/下降跳空三法', 'size': 70,
                                                        'func': tl.CDLXSIDEGAP3METHODS}}}

TABLE_CN_STOCK_KLINE_PATTERN = {'name': 'cn_stock_pattern', 'cn': '股票K线形态',
                                'columns': TABLE_CN_STOCK_FOREIGN_KEY['columns'].copy()}
TABLE_CN_STOCK_KLINE_PATTERN['columns'].update(STOCK_KLINE_PATTERN_DATA['columns'])

TABLE_CN_STOCK_SELECTION = {'name': 'cn_stock_selection', 'cn': '综合选股',
                            'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0, 'map': 'MAX_TRADE_DATE'},
                                        'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60,
                                                 'map': 'SECURITY_CODE'},
                                        'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 70,
                                                 'map': 'SECURITY_NAME_ABBR'},
                                        'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70, 'map': 'NEW_PRICE'},
                                        'change_rate': {'type': FLOAT, 'cn': '涨跌幅', 'size': 70,
                                                        'map': 'CHANGE_RATE'},
                                        'volume_ratio': {'type': FLOAT, 'cn': '量比', 'size': 70,
                                                         'map': 'VOLUME_RATIO'},
                                        'high_price': {'type': FLOAT, 'cn': '最高价', 'size': 70, 'map': 'HIGH_PRICE'},
                                        'low_price': {'type': FLOAT, 'cn': '最低价', 'size': 70, 'map': 'LOW_PRICE'},
                                        'pre_close_price': {'type': FLOAT, 'cn': '昨收价', 'size': 70,
                                                            'map': 'PRE_CLOSE_PRICE'},
                                        'volume': {'type': BIGINT, 'cn': '成交量', 'size': 90, 'map': 'VOLUME'},
                                        'deal_amount': {'type': BIGINT, 'cn': '成交额', 'size': 100,
                                                        'map': 'DEAL_AMOUNT'},
                                        'turnoverrate': {'type': FLOAT, 'cn': '换手率', 'size': 70,
                                                         'map': 'TURNOVERRATE'},
                                        'listing_date': {'type': DATE, 'cn': '上市时间', 'size': 110,
                                                         'map': 'LISTING_DATE'},
                                        'industry': {'type': VARCHAR(50, _COLLATE), 'cn': '行业', 'size': 100,
                                                     'map': 'INDUSTRY'},
                                        'area': {'type': VARCHAR(50, _COLLATE), 'cn': '地区', 'size': 70, 'map': 'AREA'},
                                        'concept': {'type': VARCHAR(800, _COLLATE), 'cn': '概念', 'size': 150,
                                                    'map': 'CONCEPT'},
                                        'style': {'type': VARCHAR(255, _COLLATE), 'cn': '板块', 'size': 150,
                                                  'map': 'STYLE'},
                                        'is_hs300': {'type': VARCHAR(2, _COLLATE), 'cn': '沪300', 'size': 0,
                                                     'map': 'IS_HS300'},
                                        'is_sz50': {'type': VARCHAR(2, _COLLATE), 'cn': '上证50', 'size': 0,
                                                    'map': 'IS_SZ50'},
                                        'is_zz500': {'type': VARCHAR(2, _COLLATE), 'cn': '中证500', 'size': 0,
                                                     'map': 'IS_ZZ500'},
                                        'is_zz1000': {'type': VARCHAR(2, _COLLATE), 'cn': '中证1000', 'size': 0,
                                                      'map': 'IS_ZZ1000'},
                                        'is_cy50': {'type': VARCHAR(2, _COLLATE), 'cn': '创业板50', 'size': 0,
                                                    'map': 'IS_CY50'},
                                        'pe9': {'type': FLOAT, 'cn': '市盈率TTM', 'size': 70, 'map': 'PE9'},
                                        'pbnewmrq': {'type': FLOAT, 'cn': '市净率MRQ', 'size': 70, 'map': 'PBNEWMRQ'},
                                        'pettmdeducted': {'type': FLOAT, 'cn': '市盈率TTM扣非', 'size': 70,
                                                          'map': 'PETTMDEDUCTED'},
                                        'ps9': {'type': FLOAT, 'cn': '市销率TTM', 'size': 70, 'map': 'PS9'},
                                        'pcfjyxjl9': {'type': FLOAT, 'cn': '市现率TTM', 'size': 70, 'map': 'PCFJYXJL9'},
                                        'predict_pe_syear': {'type': FLOAT, 'cn': '预测市盈率今年', 'size': 70,
                                                             'map': 'PREDICT_PE_SYEAR'},
                                        'predict_pe_nyear': {'type': FLOAT, 'cn': '预测市盈率明年', 'size': 70,
                                                             'map': 'PREDICT_PE_NYEAR'},
                                        'total_market_cap': {'type': BIGINT, 'cn': '总市值', 'size': 120,
                                                             'map': 'TOTAL_MARKET_CAP'},
                                        'free_cap': {'type': BIGINT, 'cn': '流通市值', 'size': 120, 'map': 'FREE_CAP'},
                                        'dtsyl': {'type': FLOAT, 'cn': '动态市盈率', 'size': 70, 'map': 'DTSYL'},
                                        'ycpeg': {'type': FLOAT, 'cn': '预测PEG', 'size': 70, 'map': 'YCPEG'},
                                        'enterprise_value_multiple': {'type': FLOAT, 'cn': '企业价值倍数', 'size': 70,
                                                                      'map': 'ENTERPRISE_VALUE_MULTIPLE'},
                                        'basic_eps': {'type': FLOAT, 'cn': '每股收益', 'size': 70, 'map': 'BASIC_EPS'},
                                        'bvps': {'type': FLOAT, 'cn': '每股净资产', 'size': 70, 'map': 'BVPS'},
                                        'per_netcash_operate': {'type': FLOAT, 'cn': '每股经营现金流', 'size': 70,
                                                                'map': 'PER_NETCASH_OPERATE'},
                                        'per_fcfe': {'type': FLOAT, 'cn': '每股自由现金流', 'size': 70,
                                                     'map': 'PER_FCFE'},
                                        'per_capital_reserve': {'type': FLOAT, 'cn': '每股资本公积', 'size': 70,
                                                                'map': 'PER_CAPITAL_RESERVE'},
                                        'per_unassign_profit': {'type': FLOAT, 'cn': '每股未分配利润', 'size': 70,
                                                                'map': 'PER_UNASSIGN_PROFIT'},
                                        'per_surplus_reserve': {'type': FLOAT, 'cn': '每股盈余公积', 'size': 70,
                                                                'map': 'PER_SURPLUS_RESERVE'},
                                        'per_retained_earning': {'type': FLOAT, 'cn': '每股留存收益', 'size': 70,
                                                                 'map': 'PER_RETAINED_EARNING'},
                                        'parent_netprofit': {'type': BIGINT, 'cn': '归属净利润', 'size': 110,
                                                             'map': 'PARENT_NETPROFIT'},
                                        'deduct_netprofit': {'type': BIGINT, 'cn': '扣非净利润', 'size': 110,
                                                             'map': 'DEDUCT_NETPROFIT'},
                                        'total_operate_income': {'type': BIGINT, 'cn': '营业总收入', 'size': 120,
                                                                 'map': 'TOTAL_OPERATE_INCOME'},
                                        'roe_weight': {'type': FLOAT, 'cn': '净资产收益率ROE', 'size': 70,
                                                       'map': 'ROE_WEIGHT'},
                                        'jroa': {'type': FLOAT, 'cn': '总资产净利率ROA', 'size': 70, 'map': 'JROA'},
                                        'roic': {'type': FLOAT, 'cn': '投入资本回报率ROIC', 'size': 70, 'map': 'ROIC'},
                                        'zxgxl': {'type': FLOAT, 'cn': '最新股息率', 'size': 70, 'map': 'ZXGXL'},
                                        'sale_gpr': {'type': FLOAT, 'cn': '毛利率', 'size': 70, 'map': 'SALE_GPR'},
                                        'sale_npr': {'type': FLOAT, 'cn': '净利率', 'size': 70, 'map': 'SALE_NPR'},
                                        'netprofit_yoy_ratio': {'type': FLOAT, 'cn': '净利润增长率', 'size': 70,
                                                                'map': 'NETPROFIT_YOY_RATIO'},
                                        'deduct_netprofit_growthrate': {'type': FLOAT, 'cn': '扣非净利润增长率',
                                                                        'size': 70,
                                                                        'map': 'DEDUCT_NETPROFIT_GROWTHRATE'},
                                        'toi_yoy_ratio': {'type': FLOAT, 'cn': '营收增长率', 'size': 70,
                                                          'map': 'TOI_YOY_RATIO'},
                                        'netprofit_growthrate_3y': {'type': FLOAT, 'cn': '净利润3年复合增长率',
                                                                    'size': 70,
                                                                    'map': 'NETPROFIT_GROWTHRATE_3Y'},
                                        'income_growthrate_3y': {'type': FLOAT, 'cn': '营收3年复合增长率', 'size': 70,
                                                                 'map': 'INCOME_GROWTHRATE_3Y'},
                                        'predict_netprofit_ratio': {'type': FLOAT, 'cn': '预测净利润同比增长',
                                                                    'size': 70,
                                                                    'map': 'PREDICT_NETPROFIT_RATIO'},
                                        'predict_income_ratio': {'type': FLOAT, 'cn': '预测营收同比增长', 'size': 70,
                                                                 'map': 'PREDICT_INCOME_RATIO'},
                                        'basiceps_yoy_ratio': {'type': FLOAT, 'cn': '每股收益同比增长率', 'size': 70,
                                                               'map': 'BASICEPS_YOY_RATIO'},
                                        'total_profit_growthrate': {'type': FLOAT, 'cn': '利润总额同比增长率',
                                                                    'size': 70,
                                                                    'map': 'TOTAL_PROFIT_GROWTHRATE'},
                                        'operate_profit_growthrate': {'type': FLOAT, 'cn': '营业利润同比增长率',
                                                                      'size': 70,
                                                                      'map': 'OPERATE_PROFIT_GROWTHRATE'},
                                        'debt_asset_ratio': {'type': FLOAT, 'cn': '资产负债率', 'size': 70,
                                                             'map': 'DEBT_ASSET_RATIO'},
                                        'equity_ratio': {'type': FLOAT, 'cn': '产权比率', 'size': 70,
                                                         'map': 'EQUITY_RATIO'},
                                        'equity_multiplier': {'type': FLOAT, 'cn': '权益乘数', 'size': 70,
                                                              'map': 'EQUITY_MULTIPLIER'},
                                        'current_ratio': {'type': FLOAT, 'cn': '流动比率', 'size': 70,
                                                          'map': 'CURRENT_RATIO'},
                                        'speed_ratio': {'type': FLOAT, 'cn': '速动比率', 'size': 70,
                                                        'map': 'SPEED_RATIO'},
                                        'total_shares': {'type': BIGINT, 'cn': '总股本', 'size': 120,
                                                         'map': 'TOTAL_SHARES'},
                                        'free_shares': {'type': BIGINT, 'cn': '流通股本', 'size': 120,
                                                        'map': 'FREE_SHARES'},
                                        'holder_newest': {'type': BIGINT, 'cn': '最新股东户数', 'size': 80,
                                                          'map': 'HOLDER_NEWEST'},
                                        'holder_ratio': {'type': FLOAT, 'cn': '股东户数增长率', 'size': 70,
                                                         'map': 'HOLDER_RATIO'},
                                        'hold_amount': {'type': FLOAT, 'cn': '户均持股金额', 'size': 80,
                                                        'map': 'HOLD_AMOUNT'},
                                        'avg_hold_num': {'type': FLOAT, 'cn': '户均持股数量', 'size': 70,
                                                         'map': 'AVG_HOLD_NUM'},
                                        'holdnum_growthrate_3q': {'type': FLOAT, 'cn': '户均持股数季度增长率',
                                                                  'size': 70,
                                                                  'map': 'HOLDNUM_GROWTHRATE_3Q'},
                                        'holdnum_growthrate_hy': {'type': FLOAT, 'cn': '户均持股数半年增长率',
                                                                  'size': 70,
                                                                  'map': 'HOLDNUM_GROWTHRATE_HY'},
                                        'hold_ratio_count': {'type': FLOAT, 'cn': '十大股东持股比例合计', 'size': 70,
                                                             'map': 'HOLD_RATIO_COUNT'},
                                        'free_hold_ratio': {'type': FLOAT, 'cn': '十大流通股东比例合计', 'size': 70,
                                                            'map': 'FREE_HOLD_RATIO'},
                                        'macd_golden_fork': {'type': BIT, 'cn': 'MACD金叉日线', 'size': 70,
                                                             'map': 'MACD_GOLDEN_FORK'},
                                        'macd_golden_forkz': {'type': BIT, 'cn': 'MACD金叉周线', 'size': 70,
                                                              'map': 'MACD_GOLDEN_FORKZ'},
                                        'macd_golden_forky': {'type': BIT, 'cn': 'MACD金叉月线', 'size': 70,
                                                              'map': 'MACD_GOLDEN_FORKY'},
                                        'kdj_golden_fork': {'type': BIT, 'cn': 'KDJ金叉日线', 'size': 70,
                                                            'map': 'KDJ_GOLDEN_FORK'},
                                        'kdj_golden_forkz': {'type': BIT, 'cn': 'KDJ金叉周线', 'size': 70,
                                                             'map': 'KDJ_GOLDEN_FORKZ'},
                                        'kdj_golden_forky': {'type': BIT, 'cn': 'KDJ金叉月线', 'size': 70,
                                                             'map': 'KDJ_GOLDEN_FORKY'},
                                        'break_through': {'type': BIT, 'cn': '放量突破', 'size': 70,
                                                          'map': 'BREAK_THROUGH'},
                                        'low_funds_inflow': {'type': BIT, 'cn': '低位资金净流入', 'size': 70,
                                                             'map': 'LOW_FUNDS_INFLOW'},
                                        'high_funds_outflow': {'type': BIT, 'cn': '高位资金净流出', 'size': 70,
                                                               'map': 'HIGH_FUNDS_OUTFLOW'},
                                        'breakup_ma_5days': {'type': BIT, 'cn': '向上突破均线5日', 'size': 70,
                                                             'map': 'BREAKUP_MA_5DAYS'},
                                        'breakup_ma_10days': {'type': BIT, 'cn': '向上突破均线10日', 'size': 70,
                                                              'map': 'BREAKUP_MA_10DAYS'},
                                        'breakup_ma_20days': {'type': BIT, 'cn': '向上突破均线20日', 'size': 70,
                                                              'map': 'BREAKUP_MA_20DAYS'},
                                        'breakup_ma_30days': {'type': BIT, 'cn': '向上突破均线30日', 'size': 70,
                                                              'map': 'BREAKUP_MA_30DAYS'},
                                        'breakup_ma_60days': {'type': BIT, 'cn': '向上突破均线60日', 'size': 70,
                                                              'map': 'BREAKUP_MA_60DAYS'},
                                        'long_avg_array': {'type': BIT, 'cn': '均线多头排列', 'size': 70,
                                                           'map': 'LONG_AVG_ARRAY'},
                                        'short_avg_array': {'type': BIT, 'cn': '均线空头排列', 'size': 70,
                                                            'map': 'SHORT_AVG_ARRAY'},
                                        'upper_large_volume': {'type': BIT, 'cn': '连涨放量', 'size': 70,
                                                               'map': 'UPPER_LARGE_VOLUME'},
                                        'down_narrow_volume': {'type': BIT, 'cn': '下跌无量', 'size': 70,
                                                               'map': 'DOWN_NARROW_VOLUME'},
                                        'one_dayang_line': {'type': BIT, 'cn': '一根大阳线', 'size': 70,
                                                            'map': 'ONE_DAYANG_LINE'},
                                        'two_dayang_lines': {'type': BIT, 'cn': '两根大阳线', 'size': 70,
                                                             'map': 'TWO_DAYANG_LINES'},
                                        'rise_sun': {'type': BIT, 'cn': '旭日东升', 'size': 70, 'map': 'RISE_SUN'},
                                        'power_fulgun': {'type': BIT, 'cn': '强势多方炮', 'size': 70,
                                                         'map': 'POWER_FULGUN'},
                                        'restore_justice': {'type': BIT, 'cn': '拨云见日', 'size': 70,
                                                            'map': 'RESTORE_JUSTICE'},
                                        'down_7days': {'type': BIT, 'cn': '七仙女下凡(七连阴)', 'size': 70,
                                                       'map': 'DOWN_7DAYS'},
                                        'upper_8days': {'type': BIT, 'cn': '八仙过海(八连阳)', 'size': 70,
                                                        'map': 'UPPER_8DAYS'},
                                        'upper_9days': {'type': BIT, 'cn': '九阳神功(九连阳)', 'size': 70,
                                                        'map': 'UPPER_9DAYS'},
                                        'upper_4days': {'type': BIT, 'cn': '四串阳', 'size': 70, 'map': 'UPPER_4DAYS'},
                                        'heaven_rule': {'type': BIT, 'cn': '天量法则', 'size': 70,
                                                        'map': 'HEAVEN_RULE'},
                                        'upside_volume': {'type': BIT, 'cn': '放量上攻', 'size': 70,
                                                          'map': 'UPSIDE_VOLUME'},
                                        'bearish_engulfing': {'type': BIT, 'cn': '穿头破脚', 'size': 70,
                                                              'map': 'BEARISH_ENGULFING'},
                                        'reversing_hammer': {'type': BIT, 'cn': '倒转锤头', 'size': 70,
                                                             'map': 'REVERSING_HAMMER'},
                                        'shooting_star': {'type': BIT, 'cn': '射击之星', 'size': 70,
                                                          'map': 'SHOOTING_STAR'},
                                        'evening_star': {'type': BIT, 'cn': '黄昏之星', 'size': 70,
                                                         'map': 'EVENING_STAR'},
                                        'first_dawn': {'type': BIT, 'cn': '曙光初现', 'size': 70, 'map': 'FIRST_DAWN'},
                                        'pregnant': {'type': BIT, 'cn': '身怀六甲', 'size': 70, 'map': 'PREGNANT'},
                                        'black_cloud_tops': {'type': BIT, 'cn': '乌云盖顶', 'size': 70,
                                                             'map': 'BLACK_CLOUD_TOPS'},
                                        'morning_star': {'type': BIT, 'cn': '早晨之星', 'size': 70,
                                                         'map': 'MORNING_STAR'},
                                        'narrow_finish': {'type': BIT, 'cn': '窄幅整理', 'size': 70,
                                                          'map': 'NARROW_FINISH'},
                                        'limited_lift_f6m': {'type': BIT, 'cn': '限售解禁未来半年', 'size': 70,
                                                             'map': 'LIMITED_LIFT_F6M'},
                                        'limited_lift_f1y': {'type': BIT, 'cn': '限售解禁未来1年', 'size': 70,
                                                             'map': 'LIMITED_LIFT_F1Y'},
                                        'limited_lift_6m': {'type': BIT, 'cn': '限售解禁近半年', 'size': 70,
                                                            'map': 'LIMITED_LIFT_6M'},
                                        'limited_lift_1y': {'type': BIT, 'cn': '限售解禁近1年', 'size': 70,
                                                            'map': 'LIMITED_LIFT_1Y'},
                                        'directional_seo_1m': {'type': BIT, 'cn': '定向增发近1个月', 'size': 70,
                                                               'map': 'DIRECTIONAL_SEO_1M'},
                                        'directional_seo_3m': {'type': BIT, 'cn': '定向增发近3个月', 'size': 70,
                                                               'map': 'DIRECTIONAL_SEO_3M'},
                                        'directional_seo_6m': {'type': BIT, 'cn': '定向增发近6个月', 'size': 70,
                                                               'map': 'DIRECTIONAL_SEO_6M'},
                                        'directional_seo_1y': {'type': BIT, 'cn': '定向增发近1年', 'size': 70,
                                                               'map': 'DIRECTIONAL_SEO_1Y'},
                                        'recapitalize_1m': {'type': BIT, 'cn': '资产重组近1个月', 'size': 70,
                                                            'map': 'RECAPITALIZE_1M'},
                                        'recapitalize_3m': {'type': BIT, 'cn': '资产重组近3个月', 'size': 70,
                                                            'map': 'RECAPITALIZE_3M'},
                                        'recapitalize_6m': {'type': BIT, 'cn': '资产重组近6个月', 'size': 70,
                                                            'map': 'RECAPITALIZE_6M'},
                                        'recapitalize_1y': {'type': BIT, 'cn': '资产重组近1年', 'size': 70,
                                                            'map': 'RECAPITALIZE_1Y'},
                                        'equity_pledge_1m': {'type': BIT, 'cn': '股权质押近1个月', 'size': 70,
                                                             'map': 'EQUITY_PLEDGE_1M'},
                                        'equity_pledge_3m': {'type': BIT, 'cn': '股权质押近3个月', 'size': 70,
                                                             'map': 'EQUITY_PLEDGE_3M'},
                                        'equity_pledge_6m': {'type': BIT, 'cn': '股权质押近6个月', 'size': 70,
                                                             'map': 'EQUITY_PLEDGE_6M'},
                                        'equity_pledge_1y': {'type': BIT, 'cn': '股权质押近1年', 'size': 70,
                                                             'map': 'EQUITY_PLEDGE_1Y'},
                                        'pledge_ratio': {'type': FLOAT, 'cn': '质押比例', 'size': 70,
                                                         'map': 'PLEDGE_RATIO'},
                                        'goodwill_scale': {'type': BIGINT, 'cn': '商誉规模', 'size': 110,
                                                           'map': 'GOODWILL_SCALE'},
                                        'goodwill_assets_ratro': {'type': FLOAT, 'cn': '商誉占净资产比例', 'size': 70,
                                                                  'map': 'GOODWILL_ASSETS_RATRO'},
                                        'predict_type': {'type': VARCHAR(10, _COLLATE), 'cn': '业绩预告', 'size': 70,
                                                         'map': 'PREDICT_TYPE'},
                                        'par_dividend_pretax': {'type': FLOAT, 'cn': '每股股利税前', 'size': 70,
                                                                'map': 'PAR_DIVIDEND_PRETAX'},
                                        'par_dividend': {'type': FLOAT, 'cn': '每股红股', 'size': 70,
                                                         'map': 'PAR_DIVIDEND'},
                                        'par_it_equity': {'type': FLOAT, 'cn': '每股转增股本', 'size': 70,
                                                          'map': 'PAR_IT_EQUITY'},
                                        'holder_change_3m': {'type': FLOAT, 'cn': '近3月股东增减比例', 'size': 70,
                                                             'map': 'HOLDER_CHANGE_3M'},
                                        'executive_change_3m': {'type': FLOAT, 'cn': '近3月高管增减比例', 'size': 70,
                                                                'map': 'EXECUTIVE_CHANGE_3M'},
                                        'org_survey_3m': {'type': SmallInteger, 'cn': '近3月机构调研', 'size': 70,
                                                          'map': 'ORG_SURVEY_3M'},
                                        'org_rating': {'type': VARCHAR(10, _COLLATE), 'cn': '机构评级', 'size': 70,
                                                       'map': 'ORG_RATING'},
                                        'allcorp_num': {'type': SmallInteger, 'cn': '机构持股家数合计', 'size': 70,
                                                        'map': 'ALLCORP_NUM'},
                                        'allcorp_fund_num': {'type': SmallInteger, 'cn': '基金持股家数', 'size': 70,
                                                             'map': 'ALLCORP_FUND_NUM'},
                                        'allcorp_qs_num': {'type': SmallInteger, 'cn': '券商持股家数', 'size': 70,
                                                           'map': 'ALLCORP_QS_NUM'},
                                        'allcorp_qfii_num': {'type': SmallInteger, 'cn': 'QFII持股家数', 'size': 70,
                                                             'map': 'ALLCORP_QFII_NUM'},
                                        'allcorp_bx_num': {'type': SmallInteger, 'cn': '保险公司持股家数', 'size': 70,
                                                           'map': 'ALLCORP_BX_NUM'},
                                        'allcorp_sb_num': {'type': SmallInteger, 'cn': '社保持股家数', 'size': 70,
                                                           'map': 'ALLCORP_SB_NUM'},
                                        'allcorp_xt_num': {'type': SmallInteger, 'cn': '信托公司持股家数', 'size': 70,
                                                           'map': 'ALLCORP_XT_NUM'},
                                        'allcorp_ratio': {'type': FLOAT, 'cn': '机构持股比例合计', 'size': 70,
                                                          'map': 'ALLCORP_RATIO'},
                                        'allcorp_fund_ratio': {'type': FLOAT, 'cn': '基金持股比例', 'size': 70,
                                                               'map': 'ALLCORP_FUND_RATIO'},
                                        'allcorp_qs_ratio': {'type': FLOAT, 'cn': '券商持股比例', 'size': 70,
                                                             'map': 'ALLCORP_QS_RATIO'},
                                        'allcorp_qfii_ratio': {'type': FLOAT, 'cn': 'QFII持股比例', 'size': 70,
                                                               'map': 'ALLCORP_QFII_RATIO'},
                                        'allcorp_bx_ratio': {'type': FLOAT, 'cn': '保险公司持股比例', 'size': 70,
                                                             'map': 'ALLCORP_BX_RATIO'},
                                        'allcorp_sb_ratio': {'type': FLOAT, 'cn': '社保持股比例', 'size': 70,
                                                             'map': 'ALLCORP_SB_RATIO'},
                                        'allcorp_xt_ratio': {'type': FLOAT, 'cn': '信托公司持股比例', 'size': 70,
                                                             'map': 'ALLCORP_XT_RATIO'},
                                        'popularity_rank': {'type': SmallInteger, 'cn': '股吧人气排名', 'size': 70,
                                                            'map': 'POPULARITY_RANK'},
                                        'rank_change': {'type': SmallInteger, 'cn': '人气排名变化', 'size': 70,
                                                        'map': 'RANK_CHANGE'},
                                        'upp_days': {'type': SmallInteger, 'cn': '人气排名连涨', 'size': 70,
                                                     'map': 'UPP_DAYS'},
                                        'down_days': {'type': SmallInteger, 'cn': '人气排名连跌', 'size': 70,
                                                      'map': 'DOWN_DAYS'},
                                        'new_high': {'type': SmallInteger, 'cn': '人气排名创新高', 'size': 70,
                                                     'map': 'NEW_HIGH'},
                                        'new_down': {'type': SmallInteger, 'cn': '人气排名创新低', 'size': 70,
                                                     'map': 'NEW_DOWN'},
                                        'newfans_ratio': {'type': FLOAT, 'cn': '新晋粉丝占比', 'size': 70,
                                                          'map': 'NEWFANS_RATIO'},
                                        'bigfans_ratio': {'type': FLOAT, 'cn': '铁杆粉丝占比', 'size': 70,
                                                          'map': 'BIGFANS_RATIO'},
                                        'concern_rank_7days': {'type': SmallInteger, 'cn': '7日关注排名', 'size': 70,
                                                               'map': 'CONCERN_RANK_7DAYS'},
                                        'browse_rank': {'type': SmallInteger, 'cn': '今日浏览排名', 'size': 70,
                                                        'map': 'BROWSE_RANK'},
                                        'amplitude': {'type': FLOAT, 'cn': '振幅', 'size': 70, 'map': 'AMPLITUDE'},
                                        'is_issue_break': {'type': BIT, 'cn': '破发股票', 'size': 70,
                                                           'map': 'IS_ISSUE_BREAK'},
                                        'is_bps_break': {'type': BIT, 'cn': '破净股票', 'size': 70,
                                                         'map': 'IS_BPS_BREAK'},
                                        'now_newhigh': {'type': BIT, 'cn': '今日创历史新高', 'size': 70,
                                                        'map': 'NOW_NEWHIGH'},
                                        'now_newlow': {'type': BIT, 'cn': '今日创历史新低', 'size': 70,
                                                       'map': 'NOW_NEWLOW'},
                                        'high_recent_3days': {'type': BIT, 'cn': '近期创历史新高近3日', 'size': 70,
                                                              'map': 'HIGH_RECENT_3DAYS'},
                                        'high_recent_5days': {'type': BIT, 'cn': '近期创历史新高近5日', 'size': 70,
                                                              'map': 'HIGH_RECENT_5DAYS'},
                                        'high_recent_10days': {'type': BIT, 'cn': '近期创历史新高近10日', 'size': 70,
                                                               'map': 'HIGH_RECENT_10DAYS'},
                                        'high_recent_20days': {'type': BIT, 'cn': '近期创历史新高近20日', 'size': 70,
                                                               'map': 'HIGH_RECENT_20DAYS'},
                                        'high_recent_30days': {'type': BIT, 'cn': '近期创历史新高近30日', 'size': 70,
                                                               'map': 'HIGH_RECENT_30DAYS'},
                                        'low_recent_3days': {'type': BIT, 'cn': '近期创历史新低近3日', 'size': 70,
                                                             'map': 'LOW_RECENT_3DAYS'},
                                        'low_recent_5days': {'type': BIT, 'cn': '近期创历史新低近5日', 'size': 70,
                                                             'map': 'LOW_RECENT_5DAYS'},
                                        'low_recent_10days': {'type': BIT, 'cn': '近期创历史新低近10日', 'size': 70,
                                                              'map': 'LOW_RECENT_10DAYS'},
                                        'low_recent_20days': {'type': BIT, 'cn': '近期创历史新低近20日', 'size': 70,
                                                              'map': 'LOW_RECENT_20DAYS'},
                                        'low_recent_30days': {'type': BIT, 'cn': '近期创历史新低近30日', 'size': 70,
                                                              'map': 'LOW_RECENT_30DAYS'},
                                        'win_market_3days': {'type': BIT, 'cn': '近期跑赢大盘近3日', 'size': 70,
                                                             'map': 'WIN_MARKET_3DAYS'},
                                        'win_market_5days': {'type': BIT, 'cn': '近期跑赢大盘近5日', 'size': 70,
                                                             'map': 'WIN_MARKET_5DAYS'},
                                        'win_market_10days': {'type': BIT, 'cn': '近期跑赢大盘近10日', 'size': 70,
                                                              'map': 'WIN_MARKET_10DAYS'},
                                        'win_market_20days': {'type': BIT, 'cn': '近期跑赢大盘近20日', 'size': 70,
                                                              'map': 'WIN_MARKET_20DAYS'},
                                        'win_market_30days': {'type': BIT, 'cn': '近期跑赢大盘近30日', 'size': 70,
                                                              'map': 'WIN_MARKET_30DAYS'},
                                        'net_inflow': {'type': FLOAT, 'cn': '当日净流入额', 'size': 110,
                                                       'map': 'NET_INFLOW'},
                                        'netinflow_3days': {'type': BIGINT, 'cn': '3日主力净流入', 'size': 110,
                                                            'map': 'NETINFLOW_3DAYS'},
                                        'netinflow_5days': {'type': BIGINT, 'cn': '5日主力净流入', 'size': 110,
                                                            'map': 'NETINFLOW_5DAYS'},
                                        'nowinterst_ratio': {'type': BIGINT, 'cn': '当日增仓占比', 'size': 70,
                                                             'map': 'NOWINTERST_RATIO'},
                                        'nowinterst_ratio_3d': {'type': FLOAT, 'cn': '3日增仓占比', 'size': 70,
                                                                'map': 'NOWINTERST_RATIO_3D'},
                                        'nowinterst_ratio_5d': {'type': FLOAT, 'cn': '5日增仓占比', 'size': 70,
                                                                'map': 'NOWINTERST_RATIO_5D'},
                                        'ddx': {'type': FLOAT, 'cn': '当日DDX', 'size': 70, 'map': 'DDX'},
                                        'ddx_3d': {'type': FLOAT, 'cn': '3日DDX', 'size': 70, 'map': 'DDX_3D'},
                                        'ddx_5d': {'type': FLOAT, 'cn': '5日DDX', 'size': 70, 'map': 'DDX_5D'},
                                        'ddx_red_10d': {'type': SmallInteger, 'cn': '10日内DDX飘红天数', 'size': 70,
                                                        'map': 'DDX_RED_10D'},
                                        'changerate_3days': {'type': FLOAT, 'cn': '3日涨跌幅', 'size': 70,
                                                             'map': 'CHANGERATE_3DAYS'},
                                        'changerate_5days': {'type': FLOAT, 'cn': '5日涨跌幅', 'size': 70,
                                                             'map': 'CHANGERATE_5DAYS'},
                                        'changerate_10days': {'type': FLOAT, 'cn': '10日涨跌幅', 'size': 70,
                                                              'map': 'CHANGERATE_10DAYS'},
                                        'changerate_ty': {'type': FLOAT, 'cn': '今年以来涨跌幅', 'size': 70,
                                                          'map': 'CHANGERATE_TY'},
                                        'upnday': {'type': SmallInteger, 'cn': '连涨天数', 'size': 70, 'map': 'UPNDAY'},
                                        'downnday': {'type': SmallInteger, 'cn': '连跌天数', 'size': 70,
                                                     'map': 'DOWNNDAY'},
                                        'listing_yield_year': {'type': FLOAT, 'cn': '上市以来年化收益率', 'size': 70,
                                                               'map': 'LISTING_YIELD_YEAR'},
                                        'listing_volatility_year': {'type': FLOAT, 'cn': '上市以来年化波动率',
                                                                    'size': 70,
                                                                    'map': 'LISTING_VOLATILITY_YEAR'},
                                        'mutual_netbuy_amt': {'type': BIGINT, 'cn': '沪深股通净买入金额', 'size': 90,
                                                              'map': 'MUTUAL_NETBUY_AMT'},
                                        'hold_ratio': {'type': FLOAT, 'cn': '沪深股通持股比例', 'size': 70,
                                                       'map': 'HOLD_RATIO'},
                                        'secucode': {'type': VARCHAR(10, _COLLATE), 'cn': '全代码', 'size': 0,
                                                     'map': 'SECUCODE'}}}

CN_STOCK_CPBD = {'name': 'cn_stock_cpbd', 'cn': '操盘必读',
                 'columns': {'SECURITY_CODE': {'type': VARCHAR(6, _COLLATE), 'cn': '代码'},
                             'SECURITY_NAME_ABBR': {'type': VARCHAR(20, _COLLATE), 'cn': '名称'},
                             'PE_DYNAMIC': {'type': FLOAT, 'cn': '市盈率动'},
                             'PE_TTM': {'type': FLOAT, 'cn': '市盈率TTM'},
                             'PE_STATIC': {'type': FLOAT, 'cn': '市盈率静'},
                             'PB_MRQ_REALTIME': {'type': FLOAT, 'cn': '市净率'},
                             'REPORT_DATE': {'type': DATE, 'cn': '财报期'},
                             'EPSJB': {'type': FLOAT, 'cn': '每股收益'},
                             'BPS': {'type': FLOAT, 'cn': '每股净资产'},
                             'MGJYXJJE': {'type': FLOAT, 'cn': '每股经营现金流'},
                             'MGZBGJ': {'type': FLOAT, 'cn': '每股公积金'},
                             'MGWFPLR': {'type': FLOAT, 'cn': '每股未分配利润'},
                             'ROEJQ': {'type': FLOAT, 'cn': '加权净资产收益率'},
                             'XSMLL': {'type': FLOAT, 'cn': '毛利率'},
                             'ZCFZL': {'type': FLOAT, 'cn': '资产负债率'},
                             'TOTAL_OPERATEINCOME': {'type': FLOAT, 'cn': '营业收入'},
                             'YYZSRGDHBZC': {'type': FLOAT, 'cn': '营业收入滚动环比增长'},
                             'TOTALOPERATEREVETZ': {'type': FLOAT, 'cn': '营业收入同比增长'},
                             'PARENT_NETPROFIT': {'type': FLOAT, 'cn': '归属净利润'},
                             'NETPROFITRPHBZC': {'type': FLOAT, 'cn': '归属净利润滚动环比增长'},
                             'PARENTNETPROFITTZ': {'type': FLOAT, 'cn': '归属净利润同比增长'},
                             'KCFJCXSYJLR': {'type': FLOAT, 'cn': '扣非净利润'},
                             'KFJLRGDHBZC': {'type': FLOAT, 'cn': '扣非净利润滚动环比增长'},
                             'KCFJCXSYJLRTZ': {'type': FLOAT, 'cn': '扣非净利润同比增长'},
                             'TOTAL_SHARE': {'type': FLOAT, 'cn': '总股本'},
                             'FREE_SHARE': {'type': FLOAT, 'cn': '流通股本'},
                             'BOARD_NAME': {'type': VARCHAR(20, _COLLATE), 'cn': '所属板块'},
                             'END_DATE': {'type': DATE, 'cn': '股东日'},
                             'HOLDER_TOTAL_NUM': {'type': FLOAT, 'cn': '股东人数'},
                             'TOTAL_NUM_RATIO': {'type': FLOAT, 'cn': '较上期变化'},
                             'AVG_FREE_SHARES': {'type': FLOAT, 'cn': '人均流通股'},
                             'AVG_FREESHARES_RATIO': {'type': FLOAT, 'cn': '较上期变化'},
                             'HOLD_FOCUS': {'type': FLOAT, 'cn': '筹码集中度'},
                             'AVG_HOLD_AMT': {'type': FLOAT, 'cn': '人均持股金额'},
                             'HOLD_RATIO_TOTAL': {'type': FLOAT, 'cn': '十大股东持股'},
                             'FREEHOLD_RATIO_TOTAL': {'type': FLOAT, 'cn': '十大流通股东持股'},
                             'LHBD_DATE': {'type': DATE, 'cn': '龙虎榜日'},
                             'EXPLANATION': {'type': FLOAT, 'cn': '龙虎说明'},
                             'TOTAL_BUY': {'type': FLOAT, 'cn': '买入金额'},
                             'TOTAL_BUYRIOTOP': {'type': FLOAT, 'cn': '买入占比'},
                             'TOTAL_SELL': {'type': FLOAT, 'cn': '卖出金额'},
                             'TOTAL_SELLRIOTOP': {'type': FLOAT, 'cn': '卖出占比'},
                             'DZJY_DATE': {'type': DATE, 'cn': '大宗交易日'},
                             'DEAL_PRICE': {'type': FLOAT, 'cn': '成交价'},
                             'PREMIUM_RATIO': {'type': FLOAT, 'cn': '折溢价率'},
                             'DEAL_VOLUME': {'type': FLOAT, 'cn': '成交量'},
                             'DEAL_AMT': {'type': FLOAT, 'cn': '成交额'},
                             'BUYER_NAME': {'type': FLOAT, 'cn': '买营业部'},
                             'SELLER_NAME': {'type': FLOAT, 'cn': '卖营业部'},
                             'RZRQ_DATE': {'type': DATE, 'cn': '融资券日'},
                             'FIN_BUY_AMT': {'type': FLOAT, 'cn': '融资买额'},
                             'FIN_REPAY_AMT': {'type': FLOAT, 'cn': '融资还额'},
                             'FIN_BALANCE': {'type': FLOAT, 'cn': '融资余额'},
                             'LOAN_SELL_VOL': {'type': FLOAT, 'cn': '融券卖量'},
                             'LOAN_REPAY_VOL': {'type': FLOAT, 'cn': '融券还量'},
                             'LOAN_BALANCE': {'type': FLOAT, 'cn': '融券余额'}}}


TABLE_CN_STOCK_CHIP_RACE_OPEN = {'name': 'cn_stock_chip_race_open', 'cn': '早盘抢筹数据',
                     'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                 'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                 'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 120},
                                 'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                 'change_rate': {'type': FLOAT, 'cn': '涨跌幅', 'size': 70},
                                 'pre_close_price': {'type': FLOAT, 'cn': '昨收', 'size': 70},
                                 'open_price': {'type': FLOAT, 'cn': '今开', 'size': 70},
                                 'deal_amount': {'type': BIGINT, 'cn': '开盘金额', 'size': 90},
                                 'bid_rate': {'type': FLOAT, 'cn': '抢筹幅度', 'size': 70},
                                 'bid_trust_amount': {'type': BIGINT, 'cn': '抢筹委托金额', 'size': 100},
                                 'bid_deal_amount': {'type': BIGINT, 'cn': '抢筹成交金额', 'size': 100},
                                 'bid_ratio': {'type': FLOAT, 'cn': '抢筹占比', 'size': 70}}}

TABLE_CN_STOCK_CHIP_RACE_END = {'name': 'cn_stock_chip_race_end', 'cn': '尾盘抢筹数据',
                     'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                 'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                 'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 120},
                                 'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                 'change_rate': {'type': FLOAT, 'cn': '涨跌幅', 'size': 70},
                                 'pre_close_price': {'type': FLOAT, 'cn': '昨收', 'size': 70},
                                 'open_price': {'type': FLOAT, 'cn': '今开', 'size': 70},
                                 'deal_amount': {'type': BIGINT, 'cn': '收盘金额', 'size': 90},
                                 'bid_rate': {'type': FLOAT, 'cn': '抢筹幅度', 'size': 70},
                                 'bid_trust_amount': {'type': BIGINT, 'cn': '抢筹委托金额', 'size': 100},
                                 'bid_deal_amount': {'type': BIGINT, 'cn': '抢筹成交金额', 'size': 100},
                                 'bid_ratio': {'type': FLOAT, 'cn': '抢筹占比', 'size': 70}}}

TABLE_CN_STOCK_LIMITUP_REASON = {'name': 'cn_stock_limitup_reason', 'cn': '涨停原因揭密',
                     'columns': {'date': {'type': DATE, 'cn': '日期', 'size': 0},
                                 'code': {'type': VARCHAR(6, _COLLATE), 'cn': '代码', 'size': 60},
                                 'name': {'type': VARCHAR(20, _COLLATE), 'cn': '名称', 'size': 120},
                                 'TITLE': {'type': VARCHAR(255, _COLLATE), 'cn': '原因', 'size': 150},
                                 'reason': {'type': VARCHAR(2000, _COLLATE), 'cn': '详因', 'size': 150},
                                 'new_price': {'type': FLOAT, 'cn': '最新价', 'size': 70},
                                 'change_rate': {'type': FLOAT, 'cn': '涨跌幅', 'size': 70},
                                 'ups_downs': {'type': FLOAT, 'cn': '涨跌额', 'size': 70},
                                 'turnoverrate': {'type': FLOAT, 'cn': '换手率', 'size': 70},
                                 'volume': {'type': BIGINT, 'cn': '成交量', 'size': 90},
                                 'deal_amount': {'type': BIGINT, 'cn': '成交额', 'size': 100},
                                 'dde': {'type': BIGINT, 'cn': 'DDE', 'size': 90}}}


def get_field_cn(key, table):
    f = table.get('columns').get(key)
    if f is None:
        return key
    return f.get('cn')


def get_field_cns(cols):
    data = []
    for k in cols:
        if k == 'code':
            data.append({"value": k, "caption": cols[k]['cn'], "width": cols[k]['size'],
                         "headerStyle": {"font": "bold 9pt Calibri", "wordWrap": "true"}, "style": ""})
        elif k == 'change_rate':
            data.append({"value": k, "caption": cols[k]['cn'], "width": cols[k]['size'],
                         "headerStyle": {"font": "bold 9pt Calibri", "wordWrap": "true"}, "conditionalFormats": [
                    {"ruleType": "formulaRule", "formula": "@>0", "style": {"foreColor": "red"}},
                    {"ruleType": "formulaRule", "formula": "@<0", "style": {"foreColor": "green"}}]})
        else:
            data.append({"value": k, "caption": cols[k]['cn'], "width": cols[k]['size'],
                         "headerStyle": {"font": "bold 9pt Calibri", "wordWrap": "true"}})
        # data.append({"value": k, "caption": cols[k]['cn'], "width": cols[k]['size'], "headerStyle": {"font": "bold 9pt Calibri", "wordWrap": "true"}})
        # data.append({"name": k, "displayName": cols[k]['cn'], "size": cols[k]['size']})
    return data


def get_field_types(cols):
    data = {}
    for k in cols:
        data[k] = cols[k]['type']
    return data


def get_field_type_name(col_type):
    if col_type == DATE:
        return "datetime"
    elif col_type == FLOAT or col_type == BIGINT or col_type == SmallInteger or col_type == BIT:
        return "numeric"
    else:
        return "string"
